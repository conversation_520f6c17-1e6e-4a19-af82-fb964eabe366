<?php
    // Asegurarse de que no estamos en modo de monitoreo de rendimiento por defecto
    if (!defined('PERFORMANCE_MONITORING')) {
        define('PERFORMANCE_MONITORING', false);
    }

    // Forzar codificación UTF-8
    header('Content-Type: text/html; charset=UTF-8');

    // Desactivar todo tipo de caché
    header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
    header('Cache-Control: post-check=0, pre-check=0', false);
    header('Pragma: no-cache');

    // Iniciar medición de rendimiento
    $start_initial_queries = microtime(true);

    // Array para almacenar tiempos de consultas individuales
    $query_times = [];

    // Función para medir tiempo de consultas SQL
    function executeQueryWithTiming($conex, $query, $queryName) {
        global $query_times;

        $start = microtime(true);
        $result = $conex->query($query);
        $end = microtime(true);

        $time = ($end - $start) * 1000; // convertir a milisegundos
        $query_times[$queryName] = [
            'time' => $time,
            'query' => $query
        ];

        return $result;
    }


    // Incluir y cargar la configuración global
    require_once 'DatabaseConnection.php';
    require_once 'GlobalConfig.php';

    // Inicializar la configuración global
    $config = GlobalConfig::getInstance();

    try {
        session_start();
        $db = DatabaseConnection::getInstance();
        $conex = $db->getConnection();

        // Guardar usuario en sesión después del login exitoso
        if (isset($aux7)) { // RUT del usuario
            $_SESSION['usuario'] = $aux7;
        }

        register_shutdown_function(function() use ($db) {
            if ($db !== null) {
                $db->cleanup();
            }
        });

    } catch (Exception $e) {
        error_log("Error de conexión: " . $e->getMessage());
        die("Error de conexión: Por favor, contacte al administrador");
    }

    // Las variables globales ahora se obtienen directamente desde GlobalConfig.php
    // No es necesario definirlas aquí ya que están en los valores por defecto


    $sesion = $_GET['id_sesion'];


    // ALTERNATIVA PARA EL SESSION////////////////////////


    // ALTERNATIVA PARA EL SESSION////////////////////////

    $sql3 = "SELECT tut.nombre, tut.email, tla.RUT, tut.id, tut.PERFIL, tut.PERFIL2,
                tut2.id as id_supervisor, tut.rut AS RUT_OR, tut.nombre_short
            FROM TB_LOG_APP tla
            LEFT JOIN tb_user_tqw tut ON tla.RUT = tut.rut
            LEFT JOIN tb_user_tqw tut2 ON tut2.email = tut.correo_super
            WHERE TOKEN = '$sesion'
            LIMIT 1";

    $start_query = microtime(true);
    $result = mysqli_query($conex, $sql3);
    $end_query = microtime(true);
    $query_times['user_session_query'] = [
        'time' => ($end_query - $start_query) * 1000,
        'query' => $sql3
    ];
    $row = mysqli_fetch_assoc($result);

    $nombre_user = $row['nombre_short'];
    $email = $row['email'];
    $perfil = $row['PERFIL'];
    $perfil2 = $row['PERFIL2'];
    $rut_ejecut = $row['RUT_OR'];
    $id_usuario = $row['id'];
    $id_supervisor = $row['id_supervisor'];
    /////////////////////////////////////////////////////
    $usuario = $row['RUT'];



    // La consulta del semáforo se ha movido a get_dash_logis.php para carga diferida
// Solo se inicializan las variables que podrían ser usadas por otras secciones

    // Variables de conteo para la sección de logística (ahora cargadas bajo demanda)
    $result23 = null; // Esta variable será reemplazada al cargar la sección logística
    $zero_pendientes_count = 0;
    $non_zero_count = 0;
    $zero_sum_job_count = 0;
    $non_zero_sum_job_count = 0;
    $zero_pendientes_reversa_count = 0;
    $non_zero_pendientes_reversa_count = 0;
    $supervisores = [];
    $cantidades = [];
    $supervisorCountsPendientes = [];
    $supervisorCountsSumJob = [];
    $supervisorCountsReversa = [];


//////////////////////////////////////////
//////////////////////////////////////////
// La consulta a tb_kpi_gerencia_calidad_EPS se ha movido a get_analytics_section.php
// y a get_calidad_reac_section.php para carga diferida
// Variables inicializadas para compatibilidad con otras secciones
$data = [];
$periods = [];
$empresas = [];
$calidad_30_values_TQW = [];
$periodo_values_TQW = [];
$tp_desc_empresas = []; // Para NACIONAL
$periodo_values_nacional = [];
$telqway_calidad_30_actual = null;
$telqway_calidad_30_antes = null;


// Las conversiones a JSON y consulta RGU se han movido a get_analytics_section.php
// Variables inicializadas para compatibilidad con otras secciones
$periods_json = '[]';
$quality_values_json = '[]';
$periodo_values_nacional_json = '[]';
$tp_desc_empresas_json = '[]';

$fechas = [];
$zona_tecnicos = [];
$zonas_unicas = [];
$rgus_por_zona = [];

// echo '<pre>';
// print_r($fechas);
// print_r($zonas_unicas);
// print_r($zona_tecnicos);
// echo '</pre>';



// La consulta a TB_KPI_GERENCIA_RGU_TECNICOS se ha movido a get_desafio_tec_section.php
// Variables inicializadas para compatibilidad con otras secciones
$all_records = [];
$records_to_display = [];



// La consulta del dashboard se ha movido a get_analytics_section.php
// Variable inicializada para compatibilidad con otras secciones
$dash = null;

// Finalizar medición de consultas iniciales
$end_initial_queries = microtime(true);
$time_initial_queries = ($end_initial_queries - $start_initial_queries) * 1000; // convertir a milisegundos

// Analizar las consultas y guardar los resultados
$query_analysis = [];
foreach ($query_times as $name => $data) {
    // Función simplificada para analizar consultas (reemplaza la llamada a analyzeSqlQuery)
    $complexity = "N/A";
    $level = "Normal";
    $issues = [];

    // Análisis básico de complejidad basado en palabras clave
    $query = strtolower($data['query']);
    if (strpos($query, 'join') !== false) {
        $joins = substr_count($query, 'join');
        if ($joins > 3) {
            $complexity = "Alta";
            $level = "Precaución";
            $issues[] = "Múltiples JOINs ($joins) detectados";
        } else {
            $complexity = "Media";
        }
    } else {
        $complexity = "Baja";
    }

    // Verificar GROUP BY sin índices
    if (strpos($query, 'group by') !== false && strpos($query, 'where') === false) {
        $complexity = "Alta";
        $level = "Precaución";
        $issues[] = "GROUP BY sin filtro WHERE";
    }

    // Verificar subconsultas
    if (substr_count($query, 'select') > 1) {
        $complexity = $complexity == "Baja" ? "Media" : "Alta";
        $level = "Precaución";
        $issues[] = "Contiene subconsultas";
    }

    $query_analysis[$name] = [
        'time' => $data['time'],
        'complexity' => $complexity,
        'level' => $level,
        'issues' => $issues
    ];
}



////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////
// La consulta a tb_kpi_gerencia_calidad_tecnico y tb_kpi_gerencia_rgu_eps
// se ha movido a get_calidad_reac_section.php
// No es necesario duplicar la inicialización de variables ya que se
// inicializaron anteriormente en este archivo.

        // Asegurarse de que todas las zonas tengan datos para todas las fechas
        foreach ($zonas_unicas as $zona) {
            if (count($zona_tecnicos[$zona]) < count($fechas)) {
                $zona_tecnicos[$zona] = array_pad($zona_tecnicos[$zona], count($fechas), null);
            }
            if (count($rgus_por_zona[$zona]) < count($fechas)) {
                $rgus_por_zona[$zona] = array_pad($rgus_por_zona[$zona], count($fechas), null);
            }
        }



        $sql_tecnicos = "SELECT DISTINCT Nombre_short
        FROM tb_user_tqw
        WHERE Nombre_short IS NOT NULL
        AND vigente = 'Si'
        AND `PERFIL` IN  ('TECNICO RESIDENCIAL','Supervisor Tecnico')
        ORDER BY Nombre_short
        ";
        $result_tecnicos = $conex->query($sql_tecnicos);



        // Primero definimos la consulta
        $sql_dotacion = "
            SELECT *
            FROM tb_user_tqw
            WHERE vigente = 'Si' AND
            PERFIL IN ('TECNICO RESIDENCIAL')
            ORDER BY Nombre_short ASC
            ";

        // Ejecutamos la consulta (asumiendo que $conn es tu conexión a la BD)
        $dotacion = mysqli_query($conex, $sql_dotacion);
?>

<?php
// Si estamos en modo de monitoreo de rendimiento, no renderizamos el HTML
if (defined('PERFORMANCE_MONITORING') && PERFORMANCE_MONITORING) {
    // Definir variables de tiempo para cada sección
    $time_analytics_section = 0;
    $time_dash_logis_section = 0;
    $time_calidad_reac_section = 0;
    $time_desafio_tec_section = 0;
    $time_cubos_datos_section = 0;
    $time_inventario_section = 0;

    // Medir tiempo de carga de analytics_section.php
    $start_section = microtime(true);
    ob_start();
    include('analytics_section.php');
    ob_end_clean();
    $end_section = microtime(true);
    $time_analytics_section = ($end_section - $start_section) * 1000;

    // Medir tiempo de carga de dash_logis_section.php
    $start_section = microtime(true);
    ob_start();
    include('dash_logis_section.php');
    ob_end_clean();
    $end_section = microtime(true);
    $time_dash_logis_section = ($end_section - $start_section) * 1000;

    // Medir tiempo de carga de desafio_tec_section.php
    $start_section = microtime(true);
    ob_start();
    include('desafio_tec_section.php');
    ob_end_clean();
    $end_section = microtime(true);
    $time_desafio_tec_section = ($end_section - $start_section) * 1000;

    // Medir tiempo de carga de cubos_datos_section.php
    $start_section = microtime(true);
    ob_start();
    include('cubos_datos_section.php');
    ob_end_clean();
    $end_section = microtime(true);
    $time_cubos_datos_section = ($end_section - $start_section) * 1000;

    // Medir tiempo de carga de inventario_section.php
    $start_section = microtime(true);
    ob_start();
    include('inventario_section.php');
    ob_end_clean();
    $end_section = microtime(true);
    $time_inventario_section = ($end_section - $start_section) * 1000;

    // No continuamos con el resto del HTML
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PORTAL OPERACIONES TELQWAY</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- ApexCharts CSS -->
    <link rel="stylesheet" href="css/apexcharts.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/sidebar.css">
    <link rel="stylesheet" href="css/app_grid.css">
    <link rel="stylesheet" href="css/tables.css">
    <link rel="stylesheet" href="css/expcard.css">
    <link rel="stylesheet" href="css/new_dash_styles.css">
    <link rel="stylesheet" href="css/lazy-loading.css">
    <link rel="stylesheet" href="css/dashboard_vtr_px.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <title>Diseño Solicitado</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js"></script> -->

    <link rel="icon" type="image/png" href="img/icons/logo.png">

    <!-- Grid Toggle Fix Script -->
    <script src="js/grid-toggle-fix.js"></script>
    <!-- User Search Fix Script -->
    <script src="js/user-search-fix.js"></script>

    <!-- Estilos específicos para gráficos VTR PX -->
    <style>

     html {
            zoom: 85%; /* Works in most modern browsers */
        }
        
        
        /* Estilos específicos para las leyendas de ApexCharts con mayor especificidad */
        /* Sobrescribir cualquier estilo problemático de common_charts.css */
        #evolutivoChart .apexcharts-legend,
        #evolutivoChart .apexcharts-legend[style*="position: absolute"],
        #evolutivoChart .apexcharts-legend[style*="top:"],
        #evolutivoChart .apexcharts-legend[style*="right:"],
        #evolutivoChart13 .apexcharts-legend,
        #evolutivoChart13 .apexcharts-legend[style*="position: absolute"],
        #evolutivoChart13 .apexcharts-legend[style*="top:"],
        #evolutivoChart13 .apexcharts-legend[style*="right:"],
        #evolutivoChart5 .apexcharts-legend,
        #evolutivoChart5 .apexcharts-legend[style*="position: absolute"],
        #evolutivoChart5 .apexcharts-legend[style*="top:"],
        #evolutivoChart5 .apexcharts-legend[style*="right:"],
        .chart-container .apexcharts-legend,
        .chart-container .apexcharts-legend[style*="position: absolute"],
        .chart-container .apexcharts-legend[style*="top:"],
        .chart-container .apexcharts-legend[style*="right:"] {
            position: static !important;
            top: auto !important;
            right: auto !important;
            left: auto !important;
            bottom: auto !important;
            display: flex !important;
            justify-content: flex-start !important;
            align-items: center !important;
            flex-wrap: wrap !important;
            padding: 10px 0 !important;
            margin: 0 !important;
            background: transparent !important;
            border-radius: 0 !important;
            width: auto !important;
            height: auto !important;
            transform: none !important;
        }
        
        #evolutivoChart .apexcharts-legend.apx-legend-position-bottom,
        #evolutivoChart .apexcharts-legend.apx-legend-position-top,
        #evolutivoChart .apexcharts-legend[class*="position-"],
        #evolutivoChart13 .apexcharts-legend.apx-legend-position-bottom,
        #evolutivoChart13 .apexcharts-legend.apx-legend-position-top,
        #evolutivoChart13 .apexcharts-legend[class*="position-"],
        #evolutivoChart5 .apexcharts-legend.apx-legend-position-bottom,
        #evolutivoChart5 .apexcharts-legend.apx-legend-position-top,
        #evolutivoChart5 .apexcharts-legend[class*="position-"],
        .chart-container .apexcharts-legend.apx-legend-position-bottom,
        .chart-container .apexcharts-legend.apx-legend-position-top,
        .chart-container .apexcharts-legend[class*="position-"] {
            position: relative !important;
            display: flex !important;
            flex-direction: row !important;
            justify-content: flex-start !important;
            align-items: center !important;
            flex-wrap: wrap !important;
            padding: 10px 0 !important;
            margin: 0 !important;
            width: 100% !important;
            height: auto !important;
        }
        
        #evolutivoChart .apexcharts-legend-series,
        #evolutivoChart13 .apexcharts-legend-series,
        #evolutivoChart5 .apexcharts-legend-series,
        .chart-container .apexcharts-legend-series {
            display: inline-flex !important;
            align-items: center !important;
            margin: 0 15px 5px 0 !important;
            padding: 0 !important;
            cursor: pointer !important;
            position: relative !important;
        }
        
        #evolutivoChart .apexcharts-legend-marker,
        #evolutivoChart13 .apexcharts-legend-marker,
        #evolutivoChart5 .apexcharts-legend-marker,
        .chart-container .apexcharts-legend-marker {
            margin-right: 8px !important;
            margin-left: 0 !important;
            margin-top: 0 !important;
            margin-bottom: 0 !important;
            width: 12px !important;
            height: 12px !important;
            border-radius: 2px !important;
            position: relative !important;
            display: inline-block !important;
        }
        
        #evolutivoChart .apexcharts-legend-text,
        #evolutivoChart13 .apexcharts-legend-text,
        #evolutivoChart5 .apexcharts-legend-text,
        .chart-container .apexcharts-legend-text {
            color: #4a5568 !important;
            font-size: 13px !important;
            font-weight: 400 !important;
            padding: 0 !important;
            margin: 0 !important;
            line-height: 1.2 !important;
            position: relative !important;
            top: 0 !important;
        }
        
        /* Asegurar que las leyendas se muestren correctamente en todas las posiciones */
        #evolutivoChart .apexcharts-legend.apx-legend-position-top,
        #evolutivoChart .apexcharts-legend[style*="top: 0px"],
        #evolutivoChart13 .apexcharts-legend.apx-legend-position-top,
        #evolutivoChart13 .apexcharts-legend[style*="top: 0px"],
        #evolutivoChart5 .apexcharts-legend.apx-legend-position-top,
        #evolutivoChart5 .apexcharts-legend[style*="top: 0px"],
        .chart-container .apexcharts-legend.apx-legend-position-top,
        .chart-container .apexcharts-legend[style*="top: 0px"] {
            top: 0 !important;
            bottom: auto !important;
            padding-top: 0 !important;
        }
        
        #evolutivoChart .apexcharts-legend.apx-legend-position-bottom,
        #evolutivoChart .apexcharts-legend[style*="bottom: 0px"],
        #evolutivoChart13 .apexcharts-legend.apx-legend-position-bottom,
        #evolutivoChart13 .apexcharts-legend[style*="bottom: 0px"],
        #evolutivoChart5 .apexcharts-legend.apx-legend-position-bottom,
        #evolutivoChart5 .apexcharts-legend[style*="bottom: 0px"],
        .chart-container .apexcharts-legend.apx-legend-position-bottom,
        .chart-container .apexcharts-legend[style*="bottom: 0px"] {
            bottom: 0 !important;
            top: auto !important;
            padding-bottom: 0 !important;
        }
    </style>

</head>

<body>

    <?php include('header_supervisor.php'); ?>

    <div id="appGrid" class="app-grid">
        <div class="app-item" data-bs-toggle="offcanvas" data-bs-target="#offcanvasrevSuper">
            <div class="app-icon"><i class="fas fa-users"></i></div>
            <span class="app-name">Usuarios</span>
        </div>

        <?php if($id_usuario == 17 || $id_usuario == 46) { ?>
        <!-- <div class="app-item" id="mejorasApp">
            <div class="app-icon"><i class="fas fa-brain"></i></div>
            <span class="app-name">Mejoras APP</span>
        </div> -->

        <div class="app-item">
            <a href="historial_anexos.php?id_sesion=<?php echo $sesion; ?>" style="text-decoration: none; color: inherit; display: flex; flex-direction: column; align-items: center; width: 100%;">
                <div class="app-icon"><i class="bi bi-file-earmark-text"></i></div>
                <span class="app-name">Generador Anexos</span>
            </a>
        </div>

        <?php } ?>

        <?php if($id_usuario == 303) { ?>
        <div class="app-item">
            <a href="Tecnico_Home_LOGIS_TEST.php?id_sesion=<?php echo $sesion; ?>">
                <div class="app-icon"><i class="fas fa-user"></i></div>
                <span class="app-name">Usuario Logistica</span>
            </a>
        </div>
        <?php } ?>

        <div class="app-item" data-bs-toggle="offcanvas" data-bs-target="#soporteTecnico">
            <div class="app-icon"><i class="bi bi-headset"></i></div>
            <span class="app-name">Soporte Tecnico</span>
        </div>

        <?php if($perfil == 'ADMIN' || $perfil == 'SUPERVISOR') { ?>
        <div class="app-item">
            <a href="config.php?id_sesion=<?php echo $sesion; ?>" style="text-decoration: none; color: inherit; display: flex; flex-direction: column; align-items: center; width: 100%;">
                <div class="app-icon"><i class="bi bi-gear-fill"></i></div>
                <span class="app-name">Configuración</span>
            </a>
        </div>
        <?php } ?>

        <div class="app-item" id="formAsignacionBtn" data-bs-toggle="offcanvas" data-bs-target="#FormSolicitud"
            aria-controls="affanOffcanvas">
            <span class="app-icon"><i class="bi bi-qr-code"></i></span>
            <span class="app-name">Form Asignación</span>
        </div>

        <!-- Nuevo botón para la sección personalizada de Nicolas -->
        <div class="app-item" id="nicolasSectionBtn" data-section="nicolas_personal">
            <div class="app-icon"><i class="bi bi-sliders"></i></div>
            <span class="app-name">Parametrico puntaje</span>
        </div>



    </div>

    <!-- Sidebar Toggle Button -->
    <button class="sidebar-toggle btn btn-link" aria-label="Toggle Sidebar">
        <i class="bi bi-list toggle-icon"></i>
    </button>

    <!-- Sidebar -->
    <aside class="sidebar">
        <div class="sidebar-brand">
            <img src="img/icons/logo.png" alt="Logo" style=" display: block; margin: 0 auto;">

        </div>
        <div class="menu-section">

            <ul class="sidebar-menu">
                <li>
                    <div class="menu-item" data-section="Home" data-toggle="submenu">
                        <i class="bi bi-house"></i>
                        <span data-i18n="nav.Home">Home</span>
                    </div>

                </li>
                <li>
                    <div class="menu-item" data-toggle="submenu">
                        <i class="bi bi-speedometer2"></i>
                        <span data-i18n="nav.dashboards">Dashboards</span>
                        <i class="bi bi-chevron-down submenu-arrow"></i>
                        <span class="badge rounded-pill" style="margin-left: 8px;">5</span>
                    </div>
                    <ul class="submenu">
                        <li><a class="menu-item" data-section="analytics" data-i18n="nav.analytics">Producción</a></li>
                        <li><a class="menu-item" data-section="calidad_reac_section" data-i18n="nav.ecommerce">Calidad Reactiva</a>
                        </li>
                        <li><a class="menu-item" data-section="dash_logis" data-i18n="nav.crm">Logistica</a></li>
                        <li><a class="menu-item" data-section="desafio_tec_section" data-i18n="nav.projects">Desafio técnico</a>
                        </li>
                        <li><a class="menu-item" data-section="marketing" data-i18n="nav.marketing">SME</a></li>
                    </ul>
                </li>
                <li>
                    <div class="menu-item" data-toggle="submenu">
                        <i class="bi bi-clipboard-check"></i>
                        <span data-i18n="nav.quality">Calidad Reactiva</span>
                        <i class="bi bi-chevron-down submenu-arrow"></i>
                        <span class="badge rounded-pill" style="margin-left: 8px;">2</span>
                    </div>

                    <ul class="submenu">
                        <li><a class="menu-item" data-section="proactive" data-i18n="nav.proactive">Proactiva</a></li>
                        <li><a class="menu-item" data-section="indicators" data-i18n="nav.indicators">Indicadores</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <div class="menu-item" data-toggle="submenu">
                        <i class="bi bi-box-seam"></i>
                        <span data-i18n="nav.quality">Logistica</span>
                        <i class="bi bi-chevron-down submenu-arrow"></i>
                        <span class="badge rounded-pill" style="margin-left: 8px;">3</span>
                    </div>
                    <ul class="submenu">
                        <li><a class="menu-item" data-section="inventario" data-i18n="nav.inventario">Cierre de
                                inventario</a>
                        </li>
                        <li><a class="menu-item" data-section="solicitudMaterial"
                                data-i18n="nav.solicitudMaterial">Solicitud material</a>
                        </li>
                        <li>
                            <a href="home_logistica_supervisor.php?id_sesion=<?php echo urlencode($sesion); ?>"
                                class="menu-item-link">
                                Ir a Home Logística Supervisor
                            </a>
                        </li>

                    </ul>
                </li>

                <li>
                    <div class="menu-item" data-section="cubos" data-toggle="submenu">
                        <i class="bi bi-graph-up"></i>
                        <span data-i18n="nav.cubos">Cubo datos</span>
                        <i class="bi bi-chart submenu-arrow"></i>
                    </div>
                </li>
                <li>
                    <div class="menu-item" data-section="reporte_produccion" data-toggle="submenu">
                        <i class="bi bi-clipboard-data"></i>
                        <span data-i18n="nav.reporte_produccion">Reporte de producción</span>
                        <i class="bi bi-chart submenu-arrow"></i>
                    </div>
                </li>
                <?php if($id_usuario == 17 || $id_usuario == 46) { ?>
                <li>
                    <div class="menu-item" data-section="facturacion" data-toggle="submenu">
                        <i class="bi bi-receipt"></i>
                        <span data-i18n="nav.facturacion">Facturación</span>
                        <i class="bi bi-chart submenu-arrow"></i>
                    </div>
                </li>
                <?php } ?>
            </ul>
        </div>
    </aside>
    <!-- Content -->
    <div class="content">
        <!-- Content Containers -->
        <div class="content-containers">

            <div id="analytics-container" class="section-container lazy-section" style="display: none;">
                <div class="loading-placeholder">
                    <div class="spinner-border text-primary"></div>
                    <p>Cargando sección de Producción...</p>
                </div>
                <div class="section-content"></div>
            </div>


            <div id="dash_logis-container" class="section-container lazy-section" style="display: none;">
                <div class="loading-placeholder">
                    <div class="spinner-border text-primary"></div>
                    <p>Cargando sección de Logística...</p>
                </div>
                <div class="section-content"></div>
            </div>
        </div>

        <div id="calidad_reac_section-container" class="section-container lazy-section" style="display: none;">
            <div class="loading-placeholder">
                <div class="spinner-border text-primary"></div>
                <p>Cargando sección de Calidad Reactiva...</p>
            </div>
            <div class="section-content"></div>
        </div>

        <div id="desafio_tec_section-container" class="section-container lazy-section" style="display: none;">
            <div class="loading-placeholder">
                <div class="spinner-border text-primary"></div>
                <p>Cargando sección de Desafío Técnico...</p>
            </div>
            <div class="section-content"></div>
        </div>

        <div id="courses-container" class="section-container" style="display: none;">
            <div class="row">
                <div class="col-md-12">
                    <div class="maintenance-box text-center">
                        <i class="fas fa-tools" style="font-size: 48px; color: #f39c12;"></i>
                        <h2>Página en desarrollo</h2>
                        <p>Volveremos pronto con mejoras</p>
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped active" role="progressbar"
                                style="width: 100%; background-color: #f39c12;">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="marketing-container" class="section-container" style="display: none;">
            <div class="row">
                <div class="col-md-12">
                    <div class="maintenance-box text-center">
                        <i class="fas fa-tools" style="font-size: 48px; color: #f39c12;"></i>
                        <h2>Página en desarrollo</h2>
                        <p>Volveremos pronto con mejoras</p>
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped active" role="progressbar"
                                style="width: 100%; background-color: #f39c12;">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div id="proactive-container" class="section-container" style="display: none;">

            <div class="col-lg-12 col-md-12 mb-4 custom-column" style="margin-top: 30px;">

                <div class="container d-flex align-items-center gap-3 mb-3">
                    <!-- Card de última actualización -->
                    <div class="d-flex align-items-center">
                        <div class="card shadow-sm">
                            <div class="card-body d-flex align-items-center">
                                <span class="kpi-icon icon1 me-3"></span>
                                <div>
                                    <h6 class="card-title mb-1">Última actualización Proactiva</h6>
                                    <p class="card-text fw-bold mb-0">
                                        <span id="fecha_carga_calidad_pro"></span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Combo box de técnicos -->
                    <div>
                        <select id="tecnicoFilter" class="column-search btn btn-primary" data-column="4"
                            style="padding: 8px 16px; border-radius: 4px; border: none; color: white; background-color: #007bff; cursor: pointer;">
                            <option value="">Selecciona supervisor</option>
                            <option value="ARIAS">ARIAS</option>
                            <option value="BARRERA">BARRERA</option>
                            <option value="CORROTEA">CORROTEA</option>
                            <option value="GOMEZ">GOMEZ</option>
                            <option value="GUERRERO">GUERRERO</option>
                        </select>
                    </div>

                    <!-- Nuevo Combo box Hoy-Futuro -->
                    <div>
                        <select id="periodoFilter" class="column-search btn btn-primary" data-column="5"
                            style="padding: 8px 16px; border-radius: 4px; border: none; color: white; background-color: #007bff; cursor: pointer;">
                            <option value="">Selecciona período</option>
                            <option value="HOY">HOY</option>
                            <option value="FUTURO">FUTURO</option>
                        </select>
                    </div>

                    <!-- Botón de descarga -->
                    <div class="download-button">
                        <button id="downloadTable_cal_proactiva" class="btn btn-success">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>

                <div id="tablesContainer">
                    <div id="tablaLogisticaContainer">
                        <div class="table-wrapper">
                            <table id="tablaCalidadProactiva" class="tabla-styled"
                                style="border-spacing: 0; border: 1px solid #d3d3d3; width: 100%;">

                                <thead>
                                    <tr>
                                        <?php
                                            $headers = ['Estado','Periodo', 'Fecha2', 'Actividad2', 'Tecnico2', 'Orden2', 'N_visitas_30', 'Equipo_ult_visita',
                                                    'Técnico1', 'Fecha1', 'TipoActividad1', 'RutCliente', 'NombrePersona', 'Estado1',
                                                    'CódigoCierre1', 'Estado2'];

                                            foreach ($headers as $index => $header) {
                                                echo '<th style="font-size: 12px;">
                                                        ' . htmlspecialchars($header) . '
                                                        <i class="bi bi-arrow-down-up sort-icon" data-column="' . $index . '"></i>
                                                    </th>';
                                            }
                                            ?>
                                    </tr>
                                    <tr>
                                        <?php
                                            foreach ($headers as $index => $header) {
                                                echo '<th><input type="text" class="column-search" data-column="' . $index . '" placeholder="Buscar ' . htmlspecialchars($header) . '"></th>';
                                            }
                                            ?>
                                    </tr>
                                </thead>

                                <?php
                                $fecha_registro = "";


                                $sql = "
                                SELECT A.*, B.estado
                                FROM tb_calidad_plan_proactivo_hoy A
                                LEFT JOIN (
                                    SELECT max(id) as max_id, orden, estado
                                    FROM tb_calidad_proactiva_estados
                                    GROUP BY orden
                                ) B ON A.`Orden2` = B.orden
                            ";

                                $result = $conex->query($sql);
                                        while ($fila = $result->fetch_assoc()) {

                                            $fecha_registro = $fila['fecha_registro'];


                                            echo '<tr>';
                                            echo '<td style="font-size: 10px; text-align: center;">';
                                            echo '<select class="estado-select" style="margin-bottom: 5px;">';
                                            echo '<option value="">Seleccionar</option>';

                                            $estados = [
                                                'en seguimiento QA',
                                                'Completada ayer',
                                                'en terreno hoy',
                                                'cancelada',
                                                'posible visita'
                                            ];

                                            // Aseguramos que el estado de la base de datos esté limpio
                                            $estadoActual = trim($fila['estado'] ?? '');

                                            foreach ($estados as $estadoOption) {
                                                // Comparamos los estados después de limpiarlos
                                                $selected = (strtolower(trim($estadoOption)) === strtolower($estadoActual)) ? 'selected' : '';
                                                echo "<option value='" . htmlspecialchars($estadoOption) . "' {$selected}>" . htmlspecialchars($estadoOption) . "</option>";
                                            }

                                            echo '</select>';
                                            echo '<button class="btn btn-primary btn-save-estado" data-orden="' . htmlspecialchars($fila['Orden2']) . '" style="font-size: 10px;">';
                                            echo '<i class="bi bi-save"></i>';
                                            echo '</button>';
                                            echo '</td>';

                                            echo '<td style="font-size: 10px; text-align: center;">' . htmlspecialchars($fila['Periodo']) . '</td>';
                                            echo '<td style="font-size: 10px; text-align: center;">' . htmlspecialchars($fila['Fecha2']) . '</td>';
                                            echo '<td style="font-size: 10px; text-align: center;">' . htmlspecialchars($fila['Actividad2']) . '</td>';
                                            echo '<td style="font-size: 10px; text-align: center;">' . htmlspecialchars($fila['Tecnico2']) . '</td>';
                                            echo '<td style="font-size: 10px; text-align: center;">' . htmlspecialchars($fila['Orden2']) . '</td>';
                                            echo '<td style="font-size: 10px; text-align: center;">' . htmlspecialchars($fila['N_visitas_30']) . '</td>';
                                            echo '<td style="font-size: 10px; text-align: center;">' . htmlspecialchars($fila['Equipo_ult_visita']) . '</td>';
                                            echo '<td style="font-size: 10px; text-align: center;">' . htmlspecialchars($fila['Técnico1']) . '</td>';
                                            echo '<td style="font-size: 10px; text-align: center;">' . htmlspecialchars($fila['Fecha1']) . '</td>';
                                            echo '<td style="font-size: 10px; text-align: center;">' . htmlspecialchars($fila['TipoActividad1']) . '</td>';
                                            echo '<td style="font-size: 10px; text-align: center;">' . htmlspecialchars($fila['RutCliente']) . '</td>';
                                            echo '<td style="font-size: 10px; text-align: center;">' . htmlspecialchars($fila['NombrePersona']) . '</td>';
                                            echo '<td style="font-size: 10px; text-align: center;">' . htmlspecialchars($fila['Estado1']) . '</td>';
                                            echo '<td style="font-size: 10px; text-align: center;">' . htmlspecialchars($fila['CódigoCierre1']) . '</td>';
                                            echo '<td style="font-size: 10px; text-align: center;">' . htmlspecialchars($fila['Estado2']) . '</td>';
                                            echo '</tr>';
                                        }
                                    ?>

                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="cubos-container" class="section-container lazy-section" style="display: none;">
            <div class="loading-placeholder">
                <div class="spinner-border text-primary"></div>
                <p>Cargando sección de Cubos de Datos...</p>
            </div>
            <div class="section-content"></div>
        </div>

        <div id="facturacion-container" class="section-container lazy-section" style="display: none;">
            <div class="loading-placeholder">
                <div class="spinner-border text-primary"></div>
                <p>Cargando sección de Facturación...</p>
            </div>
            <div class="section-content"></div>
        </div>

        <div id="inventario-container" class="section-container lazy-section" style="display: none;">
            <div class="loading-placeholder">
                <div class="spinner-border text-primary"></div>
                <p>Cargando sección de Inventario...</p>
            </div>
            <div class="section-content"></div>
        </div>

        <div id="solicitudMaterial-container" class="section-container lazy-section" style="display: none;">
            <div class="loading-placeholder">
                <div class="spinner-border text-primary"></div>
                <p>Cargando sección de Solicitudes de Materiales...</p>
            </div>
            <div class="section-content"></div>
        </div>

        <div id="Home-container" class="section-container lazy-section" style="display: block;">
            <div class="loading-placeholder">
                <div class="spinner-border text-primary"></div>
                <p>Cargando sección inicial...</p>
            </div>
            <div class="section-content"></div>
        </div>

        <div id="reporte_produccion-container" class="section-container lazy-section" style="display: none;">
            <div class="loading-placeholder">
                <div class="spinner-border text-primary"></div>
                <p>Cargando reporte de producción...</p>
            </div>
            <div class="section-content"></div>
        </div>

        <!-- Contenedor para la nueva sección personal de Nicolas -->
        <div id="nicolas_personal-container" class="section-container lazy-section" style="display: none;">
            <div class="loading-placeholder">
                <div class="spinner-border text-primary"></div>
                <p>Cargando sección personal...</p>
            </div>
            <div class="section-content"></div>
        </div>


        <div class="offcanvas offcanvas-end" id="offcanvasrevSuper" data-bs-scroll="true" tabindex="-1"
            aria-labelledby="affanOffcanvsLabel">
            <div class="offcanvas-header">
                <h5 class="offcanvas-title" id="affanOffcanvsLabel">Listado de usuarios</h5>
                <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
                    aria-label="Close"></button>
            </div>
            <div class="offcanvas-body p-4">
                <!-- Buscador -->
                <div class="mb-3">
                    <input type="text" id="userSearch" class="form-control" placeholder="Buscar usuario...">
                </div>
                <!-- Contenedor para la lista de usuarios -->
                <div id="userList">
                    <?php
                $resultado = $conex->query("SELECT t.*, w.nombre, w.area, w.supervisor, w.iden_user, w.rut, w.PERFIL
                                            FROM (SELECT a.usuario, a.pass_new, a.fecha_registro, count(*) total
                                                FROM TB_CLAVES_USUARIOS a
                                                LEFT JOIN TB_CLAVES_USUARIOS b ON a.usuario = b.usuario AND a.fecha_registro <= b.fecha_registro
                                                GROUP BY a.usuario, a.pass_new, a.fecha_registro) t
                                            LEFT JOIN tb_user_tqw w ON t.usuario = w.email
                                            WHERE t.total = 1 AND vigente = 'Si'
                                            AND PERFIL <> 'Generico Bodega'
                                            AND w.id NOT IN (17, 46)
                                            ");
                while ($fila = mysqli_fetch_assoc($resultado)) {
                    echo '<div class="card mb-2 user-card">';
                    echo '<div class="card-body p-2">';

                    $href = "Controller3.php?correo=" . urlencode($fila["usuario"]) .
                            "&clave=" . urlencode($fila["pass_new"]) .
                            "&rut=" . urlencode($fila["rut"]);

                    echo '<a class="affan-element-item d-flex justify-content-between align-items-center text-decoration-none" href="' . $href . '">';
                    echo '<span class="fw-bold user-name">' . htmlspecialchars($fila["usuario"]) . '</span>';
                    echo '<i class="bi bi-caret-right-fill fz-12"></i>';
                    echo '</a>';

                    echo '</div>';
                    echo '</div>';
                }
                ?>
                </div>
            </div>
        </div>

        <!-- Offcanvas para FORMULARIO SOPORTE TECNICO -->
        <div class="offcanvas offcanvas-end" id="soporteTecnico" data-bs-scroll="true" tabindex="-1"
            aria-labelledby="affanOffcanvsLabel">
            <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
                aria-label="Close"></button>

            <div class="offcanvas-body p-4">
                <h5>Formulario soporte logistico</h5>

                <form id="FormSoporteTecnico">

                    <!-- <div class="mb-2">
                            <label for="usuario_origen" class="form-label">Serie</label>
                            <input type="text" class="form-control" id="serieRever_nueva" name="serieRever_nueva" min="1"
                                max="500" required>
                        </div> -->

                    <div class="mb-2">
                        <label for="modulo" class="form-label">Pool afectado</label>
                        <select class="form-select" id="soporte_modulo" name="soporte_modulo" required>
                            <option value="" selected disabled>Seleccione un pool</option>
                            <option value="Pool Directa">Pool Directa</option>
                            <option value="Pool Reversa">Pool Reversa</option>
                            <option value="Pool Asignación">Pool Asignación</option>
                            <option value="Pool Faltante">Pool Faltante</option>
                            <option value="Pool Solicitud material">Pool Solicitud material</option>
                            <option value="Pool Ingreso reversa manual">Pool Ingreso reversa manual</option>
                        </select>
                    </div>

                    <div class="mb-2">
                        <label for="observaciones" class="form-label">Observaciones</label>
                        <textarea class="form-control" id="soporte_observaciones" name="soporte_observaciones" rows="4"
                            required></textarea>
                    </div>

                    <div class="mb-2">
                        <label for="complemento" class="form-label">Complemento</label>
                        <textarea class="form-control" id="soporte_complemento" name="soporte_complemento" rows="4"
                            required></textarea>
                    </div>

                    <a class="btn m-1 btn-info" onclick="enviarDatosSoporte()">
                        <i class="bi bi-cursor"></i> Ingresar observación
                    </a>
                </form>
            </div>
        </div>

        <!-- Offcanvas Justifica Directa  -->
        <div class="offcanvas offcanvas-end" id="offcanvaJustiDirecta" data-bs-scroll="true" tabindex="-1"
            aria-labelledby="affanOffcanvsLabel">

            <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
                aria-label="Close"></button>

            <!-- Offcanvas Body with Form -->
            <div class="offcanvas-body p-4">
                <h5>Canal de justificación</h5>

                <div class="mb-3">
                    <label for="serie_tran" class="form-label">SERIE SELECCIONADA</label>
                    <input type="text" class="form-control" id="serieDirectaJustifica" name="serieDirectaJustifica" value=""
                        readonly required style="background-color: #f0f0f0;">
                </div>

                <div class="mb-3">
                    <label for="serie_tran" class="form-label">Tecnico Origen</label>
                    <input style="width:70%; background-color: #f0f0f0;" type="text" class="form-control"
                        id="tecnico_origen_sol" name="ordenDirecta" value="" readonly>
                </div>

                <div class="mb-3" style="display:none;">
                    <input style="width:70%; background-color: #f0f0f0;" type="text" class="form-control"
                        id="id_valor_tecnico" name="id_valor_tecnico" value="" readonly>
                </div>

                <div class="mb-3" id="motivo_tran_contain">
                    <label id="serie_incorrecta_directa" for="motivoTecnicoDirecta" class="form-label">MOTIVO ESCALAMIENTO
                        DE TECNICO</label>
                    <!-- Cambiar el <input> por <textarea> -->
                    <textarea class="form-control" id="motivoTecnicoDirecta" name="motivoTecnicoDirecta" rows="2" required
                        readonly style="background-color: #f0f0f0;"></textarea>
                </div>
                <hr>

                <div class="mb-3">
                    <label for="serie_tran" class="form-label">RUT CLIENTE</label>
                    <input style="width:70%;" type="text" class="form-control" id="rut_clienteDirecta" name="ordenDirecta"
                        value="">
                </div>

                <div class="mb-3">
                    <label for="serie_tran" class="form-label">ORDEN DE TRABAJO</label>
                    <input style="width:70%;" type="text" class="form-control" id="ordenDirecta" name="ordenDirecta"
                        value="" readonly required>
                </div>

                <div class="mb-3" id="serieING_contain">
                    <label id="labelserieIng" for="motivoTecnicoDirecta" class="form-label">SERIE INGRESADA</label>
                    <!-- Cambiar el <input> por <textarea> -->
                    <input style="width:70%;" type="text" class="form-control" id="serieING" name="serieING" required
                        readonly>
                </div>

                <h5> ESCALAMIENTO A BODEGA </h5>

                <div class="dropdown">
                    <button class="btn btn-info dropdown-toggle" type="button" id="dropdownMenuButton"
                        data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-gear"></i> Seleccionar opción
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                        <li>
                            <a class="dropdown-item btn-danger" href="#" data-motivo="Serie Instalada" id="InstaladaLink">
                                <i class="bi bi-cursor"></i> Instalada OK y no en M06
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item btn-danger" href="#"
                                data-motivo="Serie no coincide fisico vs sistematico" id="SerieLink">
                                <i class="bi bi-cursor"></i> Serie no coincide (fisico / sistematico)
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item btn-danger" href="#" data-motivo="Corresponde Robo" id="roboLink">
                                <i class="bi bi-cursor"></i> Corresponde robo
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item btn-danger" href="#" data-motivo="Corresponde perdida de tecnico"
                                id="perdidaLink">
                                <i class="bi bi-cursor"></i> Corresponde perdida tecnico
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#" data-motivo="Corresponde perdida de cliente"
                                id="botonPerdida">
                                <i class="bi bi-cursor"></i> Corresponde perdida cliente
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#" data-motivo="Retiro serie incorrecta" id="bodegaSistemico">
                                <i class="bi bi-cursor"></i> Retiro serie incorrecta
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#" data-motivo="Error GSA" id="botonGSA">
                                <i class="bi bi-cursor"></i> Error GSA
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#" data-motivo="Mal desprovisionada" id="botonDesprovi">
                                <i class="bi bi-cursor"></i> Mal desprovisionada
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#" data-motivo="Serie instalada reversa" id="botoninstalada">
                                <i class="bi bi-cursor"></i> Serie instalada reversa
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#" data-motivo="Oracle problema sistema" id="botonOracle">
                                <i class="bi bi-cursor"></i> Oracle problema sistema
                            </a>
                        </li>

                        <li>
                            <a class="dropdown-item" href="#" data-motivo="TecnicoConFaltante" id="TecnicoConFaltante">
                                <i class="bi bi-cursor"></i> Tecnico tiene el seriado fisico
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#" data-motivo="SerieDevuelta" id="SerieDevuelta">
                                <i class="bi bi-cursor"></i> Serie devuelta a bodega
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="mb-3" id="motivo_tran_contain" style="margin-top: 20px;">
                    <label id="serie_incorrecta_directa" for="motivo_justifica" class="form-label">MOTIVO
                        JUSTIFICACION</label>
                    <input type="text" class="form-control" id="motivo_justifica" name="motivo_justifica" value="" readonly
                        required style="background-color: #f0f0f0;">
                </div>

                <div class="mb-3">
                    <label for="observacionSuper" class="form-label">Observación</label>
                    <textarea class="form-control" id="observacionSuperDirecta" name="observacionSuperDirecta"></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label" for="customFile3">Cargar Archivo</label>
                    <input class="form-control border-0" name="fileJustificaDirecta" id="fileJustificaDirecta" type="file">
                </div>

                <!-- <button type="button" class="btn btn-success" id="transferButton" style="margin-top: 52px;" onclick="hacerAlgo()"></button> -->

                <a href="https://forms.gle/Ff5Kvfh4eXue77AcA" target="_blank" class="btn btn-primary mb-2">
                    <i class="bi bi-link-45deg"></i> Formulario google evidencias
                </a>

                <button type="button" class="btn btn-success" id="transferButton" style="margin-top: 22px;"
                    onclick="hacerAlgo()">
                    <i class="bi bi-send"></i> Enviar justificación
                </button>

            </div>
        </div>

        <!-- Offcanvas HISTORIAL  -->

        <div class="offcanvas offcanvas-end" id="offcanvasHistorial" data-bs-scroll="true" tabindex="-1"
            aria-labelledby="affanOffcanvsLabel">
            <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
                aria-label="Close"></button>
            <!-- Offcanvas Body with Form -->
            <div class="offcanvas-body p-4">
                <h5>Historial de movimiento</h5>
                <div class="mb-3">
                    <label for="icon_bootstrap" class="form-label">SERIE</label>
                    <input type="text" class="form-control" placeholder="" id="serieHistorial" name="serieHistorial"
                        readonly>
                </div>
                <div class="mb-3">
                    <label for="icon_bootstrap" class="form-label">FAMILIA</label>
                    <input type="text" class="form-control" placeholder="" id="serieHistorial" name="serieHistorial"
                        readonly>
                </div>
                <div class="timeline-container" id="webHistorial">
                </div>
            </div>
        </div>

        <!-- Offcanvas PARA HISTORIAL -->
        <div class="navbar--toggler" id="canvaHistorial" data-bs-toggle="offcanvas" data-bs-target="#offcanvasHistorial"
            aria-controls="affanOffcanvas" style="border: thin; display: none;">
            Mostrar Offcanvas
        </div>

        <!-- Offcanvas para FORMULARIO DE SOLICITUD DE MATERIALES -->
        <div class="offcanvas offcanvas-end" id="FormSolicitud" data-bs-scroll="true" tabindex="-1"
            aria-labelledby="affanOffcanvsLabel">
            <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
                aria-label="Close"></button>

            <div class="offcanvas-body p-4">
                <h5>Formulario solicitud / asignación de materiales</h5>

                <h7>ASIGNACIÓN</h7>
                <div class="mb-3">

                    <div class="mb-3" id="tecnicoTransf">
                        <label for="tecnico_select" class="form-label">Técnico a quien transfiere</label>
                        <select class="form-control" id="tecnicoTraspaso" name="tecnicoTraspaso" required
                            onchange="verificarTecnico()">
                            <option value="">Seleccione el técnico a asignar</option>
                            <?php
                                    if ($dotacion->num_rows > 0) {
                                        while ($row = mysqli_fetch_assoc($dotacion)) {
                                            echo "<option value=\"" . $row["id"] . "\" data-nombre-short=\"" . $row["Nombre_short"] . "\">" . $row["Nombre_short"] . "</option>";
                                        }
                                    }
                                    ?>
                        </select>

                        <span id="spinner" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i>
                        </span>
                    </div>

                    <label id="label_seriadoCarrito" for="seriadoCarrito" class="form-label" style="display: none;">Ingrese el Seriado</label>
                    <input type="text" class="form-control" id="seriadoCarrito" name="seriadoCarrito" required
                        style="display: none;" placeholder="Escanee o ingrese el seriado..." autocomplete="off">

                </div>

                <hr>

                <a id="btn_confirmar" class="btn m-1 btn-info" onclick="confirmarSolicitud()">
                    <i class="bi bi-cursor"></i> Confirmar solicitud
                </a>

                <script>
                    function verificarTecnico() {
                        var tecnicoSelect = document.getElementById("tecnicoTraspaso");
                        var tecnicoId = tecnicoSelect.value;
                        var tecnicoNombreShort = tecnicoSelect.options[tecnicoSelect.selectedIndex].getAttribute(
                            "data-nombre-short");
                        // Mostrar el spinner
                        document.getElementById("spinner").style.display = "inline-block";
                        // Realizar solicitud GET o POST al servidor
                        var xhr = new XMLHttpRequest();
                        xhr.onreadystatechange = function() {
                            if (xhr.readyState === XMLHttpRequest.DONE) {
                                // Ocultar el spinner
                                document.getElementById("spinner").style.display = "none";
                                if (xhr.status === 200) {
                                    var response = xhr.responseText;
                                    // if (response === "0") {
                                    // Mostrar elementos ocultos
                                    console.log(response);
                                    document.getElementById("seriadoCarrito").style.display = "block";
                                    document.getElementById("label_seriadoCarrito").style.display = "block";
                                    document.getElementById("btn_confirmar").style.display = "block";
                                    document.getElementById("carrito").style.display = "block";

                                }
                            }
                        };
                        xhr.open("GET", "GET_API.php?nombre_short=" + tecnicoNombreShort + "&proceso=get_semaforo_asignacion", true);
                        xhr.send();
                    }
                </script>

                </form>

                <div id="carrito">
                    <!-- Aquí se mostrarán las selecciones guardadas -->
                </div>

            </div>
        </div>


        <!-- Core Libraries -->
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>

        <script src="https://unpkg.com/xlsx/dist/xlsx.full.min.js"></script>

        <!-- Bootstrap Core -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <!-- Eliminamos referencia a bootstrap.min.js que es redundante con bootstrap.bundle.min.js -->

        <!-- Charting Libraries -->
        <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-3d@1.0.0/dist/chartjs-plugin-3d.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.37.0/dist/apexcharts.min.js"></script>

        <!-- Excel Export Library -->
        <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>

        <!-- Application Specific Scripts -->
        <!-- Corregimos la referencia para asegurar que usa la ruta local -->
        <script src="./js/translations.js"></script>
        <script src="./js/animations.js"></script>
        <script src="./js/main.js"></script>
        <script src="./js/sidebar.js"></script>
        <script src="./js/table-functions.js"></script>
        <script src="./js/export-tables.js"></script>
        <script src="./js/table-config.js"></script>
        <!-- Sistema Modular de Lazy Loading v3.0 -->
        <script src="./js/modular-lazy-loading.js"></script>
        
        <!-- Módulos independientes para cada sección -->
        <script>
        // Pre-cargar módulos cuando estén disponibles
        document.addEventListener('DOMContentLoaded', function() {
            // Los módulos se cargarán dinámicamente según la sección
            console.log('Sistema Modular de Lazy Loading listo');
        });
        </script>
        <script src="./js/new_dash_bundled.js"></script>
        <script src="./js/section-initializers.js"></script>
        <script src="./js/reinit-section-handlers.js"></script>
        <!-- Script para corregir el funcionamiento del seriadoCarrito -->
        <script src="./js/form_materiales.js"></script>
        <script src="./js/form_materiales_seriado_fix.js"></script>

        <!-- Estilos para el carrito de compras -->
        <link rel="stylesheet" href="./css/seriadoCarrito.css">
        <!-- Debe estar después de la tabla -->
        <script>
            console.log('Verificando carga de script SSE');
            // Definir variables globales para uso en form_materiales.js
            var userId = <?php echo $id_usuario; ?>;
            var userRut = '<?php echo $usuario; ?>';
            console.log('Variables de usuario definidas - ID:', userId, 'RUT:', userRut);
        </script>

        <script>
        // Script para manejar inicialización después de carga
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM completamente cargado, ejecutando funciones de inicialización');

            // Inicializar cuadros de diálogo, si existen
            const dialogElements = document.querySelectorAll('[data-type="dialog"]');
            dialogElements.forEach(function(dialogElement) {
                if (!dialogElement) return;

                const dialogId = dialogElement.getAttribute('data-dialog-id');
                if (!dialogId) return;

                const dialogOptions = {
                    backdrop: true,
                    keyboard: true
                };

                // Verificamos que el elemento exista antes de crear el modal
                const dialogElement = document.getElementById(dialogId);
                if (dialogElement && typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                    const dialog = new bootstrap.Modal(dialogElement, dialogOptions);

                    // Almacenar en el objeto global para acceso posterior
                    window.appDialogs = window.appDialogs || {};
                    window.appDialogs[dialogId] = dialog;
                }
            });

            // Actualizar fecha última carga en la sección calidad proactiva
            const fechaCargaElement = document.getElementById('fecha_carga_calidad_pro');
            if (fechaCargaElement) {
                // Usamos la fecha actual como respaldo
                const fechaRegistro = new Date().toLocaleDateString('es-CL');
                fechaCargaElement.textContent = fechaRegistro;
            }

            // Manejar el evento de sección cargada para inicializar componentes específicos
            document.addEventListener('sectionLoaded', function(e) {
                const sectionId = e.detail.sectionId;
                console.log('Sección cargada:', sectionId);

                // Reinicializar elementos específicos según la sección
                if (sectionId === 'calidad_reac_section') {
                    // Inicializar gráficos de calidad si es necesario
                    if (typeof Chart !== 'undefined' && document.getElementById('qualityChart')) {
                        // La inicialización del gráfico ya está en el archivo cargado
                        console.log('Gráfico de calidad encontrado y listo');
                    }
                }

                // Reinicializar tablas de ordenamiento si existen
                if (typeof initTableSorting === 'function') {
                    initTableSorting();
                }
            });

            // Solución directa para el filtro de usuarios
            const userSearch = document.getElementById('userSearch');
            if (userSearch) {
                console.log('Configurando filtro de usuarios (solución directa)');

                // Eliminar event listeners existentes
                const newUserSearch = userSearch.cloneNode(true);
                userSearch.parentNode.replaceChild(newUserSearch, userSearch);

                // Agregar nuevo event listener
                newUserSearch.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    console.log('Buscando usuarios con término:', searchTerm);

                    // Obtener todas las tarjetas de usuario dentro del offcanvas
                    const userCards = document.querySelectorAll('#offcanvasrevSuper .user-card');
                    console.log('Tarjetas encontradas:', userCards.length);

                    // Filtrar las tarjetas
                    userCards.forEach(card => {
                        const userNameElement = card.querySelector('.user-name');
                        if (userNameElement) {
                            const userName = userNameElement.textContent.toLowerCase();
                            const visible = userName.includes(searchTerm);
                            card.style.display = visible ? 'block' : 'none';
                            console.log(`Usuario: ${userName}, Visible: ${visible}`);
                        }
                    });
                });

                // También agregar para keyup
                newUserSearch.addEventListener('keyup', function() {
                    // Disparar el evento input
                    const event = new Event('input');
                    this.dispatchEvent(event);
                });

                // Agregar event listener para cuando se abra el offcanvas
                const offcanvasElement = document.getElementById('offcanvasrevSuper');
                if (offcanvasElement) {
                    offcanvasElement.addEventListener('shown.bs.offcanvas', function() {
                        console.log('Offcanvas mostrado, reiniciando búsqueda');
                        // Limpiar el campo de búsqueda
                        newUserSearch.value = '';
                        // Mostrar todas las tarjetas
                        const userCards = document.querySelectorAll('#offcanvasrevSuper .user-card');
                        userCards.forEach(card => {
                            card.style.display = 'block';
                        });
                        // Enfocar el campo de búsqueda
                        setTimeout(() => {
                            newUserSearch.focus();
                        }, 100);
                    });
                }
            }
        });
        </script>
        <?php /* Reemplazamos include con script inline */ ?>

        <!-- Script para manejar el clic en el nombre del técnico -->
        <script>
        $(document).ready(function() {
            console.log('Inicializando manejador de clic en nombre de técnico');
            

            // Click en la celda de nombre del técnico
            $(document).on('click', '#tablaLogistica tbody td:first-child', function(e) {
                e.preventDefault();
                console.log('Celda de nombre de técnico clickeada');

                var nombreShort = $(this).text().trim();
                var rut = $(this).closest('tr').find('.rut-value').val();

                console.log('Nombre:', nombreShort, 'RUT:', rut);

                // Contraer la tabla
                $('#tablaLogisticaContainer').addClass('contracted');

                // Mostrar spinner de carga en el contenedor de resultados
                $('#resultContainer').addClass('active').find('.result-content').html(
                    '<div style="text-align: center; padding: 20px;">' +
                    '<i class="fas fa-spinner fa-spin" style="font-size: 24px;"></i>' +
                    '<p>Cargando datos...</p>' +
                    '</div>'
                );

                // Realizar la petición AJAX
                $.ajax({
                    url: 'get_data.php',
                    method: 'POST',
                    data: {
                        nombre_short: nombreShort,
                        rut: rut
                    },
                    success: function(response) {
                        console.log('Respuesta recibida');

                        // Mostrar el contenedor de resultados con la respuesta
                        $('#resultContainer').find('.result-content').html(response);
                    },
                    error: function(xhr, status, error) {
                        console.error('Error:', error);
                        $('#resultContainer').find('.result-content').html(
                            '<div class="alert alert-danger">' +
                            'Error al cargar los datos: ' + error +
                            '</div>'
                        );
                    }
                });
            });

            // Cerrar el panel de resultados
            $(document).on('click', '#closeResultContainer', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Cerrando panel de resultados');

                // Expandir la tabla
                $('#tablaLogisticaContainer').removeClass('contracted');

                // Ocultar el contenedor de resultados
                $('#resultContainer').removeClass('active');
            });
        });
        </script>

</body>

</html>
