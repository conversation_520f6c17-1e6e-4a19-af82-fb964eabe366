<?php
/**
 * Footer modular para manejo de formularios dinámicos
 * Versión simplificada y escalable
 */

$currentPage = basename($_SERVER['PHP_SELF']);

// Redirigir a todos los usuarios a mod_logistica.php
$tecnico_home_url = 'mod_logistica.php'; // Ahora todos los usuarios van a mod_logistica.php



// Configuración de formularios disponibles
$formularios = [
    [
        'id' => 'materiales',
        'icono' => 'bi-box-seam',
        'titulo' => 'Solicitud de Materiales',
        'archivo' => 'forms/form_materiales.php',
        'script' => 'js/forms/form_materiales_cascada_simple.js'
    ],
    [
        'id' => 'revision',
        'icono' => 'bi-clipboard-check',
        'titulo' => 'Formulario de Revisión',
        'archivo' => 'forms/form_revision.php',
        'script' => 'js/forms/form_revision.js',
        'oculto' => true
    ],
    [
        'id' => 'soporte',
        'icono' => 'bi-headset',
        'titulo' => 'Solicitud de Soporte',
        'archivo' => 'forms/form_soporte.php',
        'script' => 'js/forms/form_soporte.js',
        'oculto' => true
    ]
];

// Obtener conexión a la base de datos si es necesaria
if (!isset($conex) && file_exists(__DIR__ . '/../DatabaseConnection.php')) {
    try {
        require_once __DIR__ . '/../DatabaseConnection.php';
        $db = DatabaseConnection::getInstance();
        $conex = $db->getConnection();
    } catch (Exception $e) {
        // Error silencioso
    }
}
?>

<!-- Estilos base del footer modular -->
<link rel="stylesheet" href="css/footer_modular.css?v=<?php echo time(); ?>">
<link rel="stylesheet" href="css/form_panel.css?v=<?php echo time(); ?>">
<link rel="stylesheet" href="css/floating_menu.css?v=<?php echo time(); ?>">
<link rel="stylesheet" href="css/form_select_normal.css?v=<?php echo time(); ?>">
<link rel="stylesheet" href="css/searchable_select.css?v=<?php echo time(); ?>">

<!-- Navegación inferior -->
<nav class="bottom-nav" id="bottom-nav">
    <!-- Iconos a la izquierda -->
    <a href="activity_dashboard.php" class="nav-item <?php echo ($currentPage == 'activity_dashboard.php') ? 'active' : ''; ?>">
        <i class="bi bi-grid"></i>
    </a>
    <a href="charts_dashboard.php" class="nav-item <?php echo ($currentPage == 'charts_dashboard.php') ? 'active' : ''; ?>">
        <i class="bi bi-activity"></i>
    </a>
    <a href="calidad_reactiva.php" class="nav-item <?php echo ($currentPage == 'calidad_reactiva.php') ? 'active' : ''; ?>">
        <i class="bi bi-file-text"></i>
    </a>

    <!-- Botón + central -->
    <div class="add-button-container">
        <div class="floating-menu" id="floatingMenu">
            <?php foreach ($formularios as $form): ?>
                <?php if ($form['id'] === 'materiales'): ?>
                <a href="#" class="floating-menu-item" data-form-id="<?php echo $form['id']; ?>">
                    <i class="bi <?php echo $form['icono']; ?>"></i> <?php echo $form['titulo']; ?>
                </a>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
        <div class="nav-item add-button" id="addButton">
            <i class="bi bi-plus"></i>
        </div>
    </div>

    <!-- Iconos a la derecha -->
    <a href="javascript:void(0)" class="nav-item" id="ticketButton">
        <i class="bi bi-ticket"></i>
    </a>
    <!-- TEMPORAL: Botón logística con modal de mantenimiento -->
    <!-- Funcionalidad original restaurada -->
    <a href="<?php echo $tecnico_home_url; ?>" class="nav-item <?php echo ($currentPage == 'Tecnico_Home_LOGIS_TEST_V2.php' || $currentPage == 'mod_logistica.php') ? 'active' : ''; ?>">
        <i class="bi bi-box"></i>
    </a>
    <a href="#" class="nav-item" id="logoutButton">
        <i class="bi bi-box-arrow-right"></i>
    </a>
</nav>

<!-- Modal de Cierre de Sesión -->
<div id="logoutModal" class="logout-modal">
  <div class="logout-modal-content">
    <div class="logout-modal-header">
      <h2>Confirmar Cierre de Sesión</h2>
    </div>
    <div class="logout-modal-body">
      <p>¿Estás seguro de que quieres cerrar la sesión?</p>
    </div>
    <div class="logout-modal-footer">
      <button id="cancelLogout" class="btn-logout-cancel">Cancelar</button>
      <a href="logout.php" id="confirmLogout" class="btn-logout-confirm">Cerrar Sesión</a>
    </div>
  </div>
</div>

<!-- Modal de Mantenimiento Logística - Comentado para restaurar funcionalidad original
<div id="mantenimientoModal" class="modal-overlay">
  <div class="modal-content">
    <span class="close-modal" id="closeMantenimientoBtn" aria-label="Cerrar">&times;</span>
    
    <div class="modal-message active">
      <h3>🔧 Módulo Logístico en Mantenimiento</h3>
      <p>Estimado técnico, informamos que el <strong>Módulo Logístico</strong> se encuentra temporalmente fuera de servicio debido a tareas de mantenimiento programado.</p>
      
      <div style="background: rgba(255, 152, 0, 0.1); padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #ff9800;">
        <h4 style="margin-top: 0; color: #ff9800;"><i class="bi bi-clock"></i> Información del Mantenimiento</h4>
        <p style="margin-bottom: 8px;"><strong>Inicio:</strong> <?php echo date('d/m/Y H:i'); ?> hrs</p>
        <p style="margin-bottom: 8px;"><strong>Finalización estimada:</strong> <?php echo date('d/m/Y'); ?> - 13:00 hrs</p>
        <p style="margin-bottom: 0;"><strong>Duración aproximada:</strong> 2-3 horas</p>
      </div>

      <h4><i class="bi bi-gear"></i> Trabajos en Progreso:</h4>
      <ul>
        <li>Actualización de base de datos</li>
        <li>Optimización del sistema de inventario</li>
        <li>Mejoras en la interfaz de usuario</li>
        <li>Pruebas de rendimiento y estabilidad</li>
      </ul>

      <p><strong>Disculpe las molestias ocasionadas.</strong> El módulo estará disponible nuevamente en el horario indicado con mejoras significativas en su funcionamiento.</p>
      
      <div style="background: rgba(0, 225, 253, 0.1); padding: 12px; border-radius: 6px; margin: 15px 0; border-left: 3px solid #00e1fd;">
        <p style="margin: 0; font-size: 0.9rem; color: rgba(255, 255, 255, 0.9);">
          <i class="bi bi-info-circle"></i> <strong>Nota:</strong> Las solicitudes de materiales pendientes no se verán afectadas y podrán ser procesadas normalmente una vez finalizado el mantenimiento.
        </p>
      </div>
    </div>
  </div>
</div>
-->

<!-- Overlays -->
<div class="menu-overlay" id="menuOverlay"></div>
<div class="form-overlay" id="formOverlay"></div>

<!-- Contenedor de formularios dinámicos -->
<div id="formsContainer">
    <?php foreach ($formularios as $form): ?>
        <div class="form-panel" id="form-<?php echo $form['id']; ?>" data-form-id="<?php echo $form['id']; ?>">
            <?php 
                // Incluir el archivo del formulario si existe
                $formPath = __DIR__ . '/../' . $form['archivo'];
                if (file_exists($formPath)) {
                    include $formPath;
                } else {
                    echo '<div class="form-placeholder">Formulario no encontrado: ' . $form['archivo'] . '</div>';
                }
            ?>
        </div>
    <?php endforeach; ?>
</div>

<!-- Scripts del sistema modular -->
<script src="js/core/footer_modular.js"></script>

<!-- Cargar scripts específicos de cada formulario -->
<?php foreach ($formularios as $form): ?>
    <?php if (isset($form['script'])): ?>
        <script src="<?php echo $form['script']; ?>"></script>
    <?php endif; ?>
<?php endforeach; ?>

<!-- Configuración de formularios disponibles para JavaScript -->
<script>
    window.FORMULARIOS_CONFIG = <?php echo json_encode($formularios); ?>;
    
    // Configuración del módulo logístico
    window.LOGISTICA_CONFIG = {
        tecnico_home_url: '<?php echo $tecnico_home_url; ?>',
        is_top_technician: <?php echo isset($id_usuario) && in_array($id_usuario, $top_technicians) ? 'true' : 'false'; ?>
    };
</script>