# Reporte de Actividades Telqway - Junio 2025

## Resumen Ejecutivo

Este reporte analiza las actividades registradas en la tabla `toa_BI_estatico` durante el mes de junio 2025 para un conjunto específico de 11 RUTs clientes. El análisis incluye el estado de las actividades, motivos de no realización y productividad técnica.

---

## 1. Análisis de Estados de Actividades

### Query Ejecutada:
```sql
SELECT 
    Estado,
    COUNT(*) as <PERSON>ti<PERSON>,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM toa_BI_estatico WHERE YEAR(fecha_format) = 2025 AND MONTH(fecha_format) = 6 AND RUT_FORMAT IN ('20189443-3', '13449606-1', '12631108-7', '19777151-8', '19485174-K', '20239971-1', '22064488-K', '16280795-1', '13091348-2', '13879192-0', '20187880-2') AND Estado IN ('Completado', 'No Realizada')), 2) as Porcentaje
FROM toa_BI_estatico 
WHERE YEAR(fecha_format) = 2025 
    AND MONTH(fecha_format) = 6
    AND RUT_FORMAT IN ('20189443-3', '13449606-1', '12631108-7', '19777151-8', '19485174-K', '20239971-1', '22064488-K', '16280795-1', '13091348-2', '13879192-0', '20187880-2')
    AND Estado IN ('Completado', 'No Realizada')
GROUP BY Estado
```

### Resultados:

| Estado | Cantidad | Porcentaje |
|--------|----------|------------|
| **Completado** | 154 | 38.21% |
| **No Realizada** | 249 | 61.79% |

**Total de actividades analizadas:** 403

---

## 2. Segmentación de Motivos - Área de Derivación

### Query Ejecutada:
```sql
SELECT 
    [Area derivación] as Area_Derivacion,
    COUNT(*) as Cantidad
FROM toa_BI_estatico 
WHERE YEAR(fecha_format) = 2025 
    AND MONTH(fecha_format) = 6
    AND RUT_FORMAT IN ('20189443-3', '13449606-1', '12631108-7', '19777151-8', '19485174-K', '20239971-1', '22064488-K', '16280795-1', '13091348-2', '13879192-0', '20187880-2')
    AND Estado = 'No Realizada'
    AND [Area derivación] IS NOT NULL AND [Area derivación] != ''
GROUP BY [Area derivación]
ORDER BY Cantidad DESC
```

### Resultados:

| Área de Derivación | Cantidad | Porcentaje del Total |
|-------------------|----------|---------------------|
| **Plataforma Reagendamiento** | 168 | 67.47% |
| **Comercial Venta** | 38 | 15.26% |
| **Gestión de Infactibilidad** | 26 | 10.44% |
| **Redes Neutra** | 8 | 3.21% |
| **GSA** | 4 | 1.61% |
| **Alta Utilización** | 2 | 0.80% |
| **Backoffice** | 2 | 0.80% |
| **Redes** | 1 | 0.40% |


### Query Ejecutada: 1️⃣ QUERY FILTRADA POR TIPO DE ACTIVIDAD: ALTA Y MIGRACIÓN
 
```sql
SELECT 
    [Area derivación] as Area_Derivacion,
    COUNT(*) as Cantidad,
    ROUND(COUNT(*) * 100.0 / (
        SELECT COUNT(*) 
        FROM toa_BI_estatico 
        WHERE YEAR(fecha_format) = 2025 
            AND MONTH(fecha_format) = 6
            AND RUT_FORMAT IN ('20189443-3', '13449606-1', '12631108-7', '19777151-8', 
                              '19485174-K', '20239971-1', '22064488-K', '16280795-1', 
                              '13091348-2', '13879192-0', '20187880-2')
            AND Estado = 'No Realizada'
            AND [Area derivación] IS NOT NULL AND [Area derivación] != ''
            AND (
                UPPER([Tipo de actividad]) LIKE '%ALTA%' 
                OR UPPER([Tipo de actividad]) LIKE '%MIGRA%'
                OR UPPER([Tipo de actividad]) LIKE '%MIGRACION%'
            )
    ), 2) as Porcentaje_del_Total
FROM toa_BI_estatico 
WHERE YEAR(fecha_format) = 2025 
    AND MONTH(fecha_format) = 6
    AND RUT_FORMAT IN ('20189443-3', '13449606-1', '12631108-7', '19777151-8', 
                      '19485174-K', '20239971-1', '22064488-K', '16280795-1', 
                      '13091348-2', '13879192-0', '20187880-2')
    AND Estado = 'No Realizada'
    AND [Area derivación] IS NOT NULL AND [Area derivación] != ''
    AND (
        UPPER([Tipo de actividad]) LIKE '%ALTA%' 
        OR UPPER([Tipo de actividad]) LIKE '%MIGRA%'
        OR UPPER([Tipo de actividad]) LIKE '%MIGRACION%'
    )
GROUP BY [Area derivación]
ORDER BY Cantidad DESC;
```


## -- ===============================================================

## -- 3️⃣ QUERY SIN FILTRO DE RUT (TODOS LOS RUTs) - JUNIO 2025
##  ===============================================================
```sql
SELECT 
    [Area derivación] as Area_Derivacion,
    COUNT(*) as Cantidad,
    ROUND(COUNT(*) * 100.0 / (
        SELECT COUNT(*) 
        FROM toa_BI_estatico 
        WHERE YEAR(fecha_format) = 2025 
            AND MONTH(fecha_format) = 6
            AND Estado = 'No Realizada'
            AND [Area derivación] IS NOT NULL AND [Area derivación] != ''
    ), 2) as Porcentaje_del_Total
FROM toa_BI_estatico 
WHERE YEAR(fecha_format) = 2025 
    AND MONTH(fecha_format) = 6
    AND Estado = 'No Realizada'
    AND [Area derivación] IS NOT NULL AND [Area derivación] != ''
GROUP BY [Area derivación]
ORDER BY Cantidad DESC;

-- ===============================================================
-- 📊 QUERY COMPARATIVA: GRUPO FOCO vs UNIVERSO COMPLETO
-- ===============================================================
-- ===============================================================
-- QUERY COMPARATIVA: GRUPO FOCO vs UNIVERSO COMPLETO
-- FILTRADA POR TIPO DE ACTIVIDAD: ALTA Y MIGRACIÓN
-- ===============================================================

WITH GrupoFoco AS (
    SELECT 
        [Area derivación] as Area_Derivacion,
        COUNT(*) as Cantidad_Foco,
        ROUND(COUNT(*) * 100.0 / (
            SELECT COUNT(*) 
            FROM toa_BI_estatico 
            WHERE YEAR(fecha_format) = 2025 
                AND MONTH(fecha_format) = 6
                AND RUT_FORMAT IN ('20189443-3', '13449606-1', '12631108-7', '19777151-8', 
                                  '19485174-K', '20239971-1', '22064488-K', '16280795-1', 
                                  '13091348-2', '13879192-0', '20187880-2')
                AND Estado = 'No Realizada'
                AND [Area derivación] IS NOT NULL AND [Area derivación] != ''
                AND (
                    UPPER([Tipo de actividad]) LIKE '%ALTA%' 
                    OR UPPER([Tipo de actividad]) LIKE '%MIGRA%'
                    OR UPPER([Tipo de actividad]) LIKE '%MIGRACION%'
                )
        ), 2) as Porcentaje_Foco
    FROM toa_BI_estatico 
    WHERE YEAR(fecha_format) = 2025 
        AND MONTH(fecha_format) = 6
        AND RUT_FORMAT IN ('20189443-3', '13449606-1', '12631108-7', '19777151-8', 
                          '19485174-K', '20239971-1', '22064488-K', '16280795-1', 
                          '13091348-2', '13879192-0', '20187880-2')
        AND Estado = 'No Realizada'
        AND [Area derivación] IS NOT NULL AND [Area derivación] != ''
        AND (
            UPPER([Tipo de actividad]) LIKE '%ALTA%' 
            OR UPPER([Tipo de actividad]) LIKE '%MIGRA%'
            OR UPPER([Tipo de actividad]) LIKE '%MIGRACION%'
        )
    GROUP BY [Area derivación]
),
UniversoCompleto AS (
    SELECT 
        [Area derivación] as Area_Derivacion,
        COUNT(*) as Cantidad_Total,
        ROUND(COUNT(*) * 100.0 / (
            SELECT COUNT(*) 
            FROM toa_BI_estatico 
            WHERE YEAR(fecha_format) = 2025 
                AND MONTH(fecha_format) = 6
                AND Estado = 'No Realizada'
                AND [Area derivación] IS NOT NULL AND [Area derivación] != ''
                AND (
                    UPPER([Tipo de actividad]) LIKE '%ALTA%' 
                    OR UPPER([Tipo de actividad]) LIKE '%MIGRA%'
                    OR UPPER([Tipo de actividad]) LIKE '%MIGRACION%'
                )
        ), 2) as Porcentaje_Total
    FROM toa_BI_estatico 
    WHERE YEAR(fecha_format) = 2025 
        AND MONTH(fecha_format) = 6
        AND Estado = 'No Realizada'
        AND [Area derivación] IS NOT NULL AND [Area derivación] != ''
        AND (
            UPPER([Tipo de actividad]) LIKE '%ALTA%' 
            OR UPPER([Tipo de actividad]) LIKE '%MIGRA%'
            OR UPPER([Tipo de actividad]) LIKE '%MIGRACION%'
        )
    GROUP BY [Area derivación]
)
SELECT 
    COALESCE(gf.Area_Derivacion, uc.Area_Derivacion) as Area_Derivacion,
    COALESCE(gf.Cantidad_Foco, 0) as Cantidad_Grupo_Foco,
    COALESCE(gf.Porcentaje_Foco, 0) as Porcentaje_Grupo_Foco,
    COALESCE(uc.Cantidad_Total, 0) as Cantidad_Universo_Total,
    COALESCE(uc.Porcentaje_Total, 0) as Porcentaje_Universo_Total,
    -- Diferencia porcentual
    ROUND(COALESCE(gf.Porcentaje_Foco, 0) - COALESCE(uc.Porcentaje_Total, 0), 2) as Diferencia_Porcentual,
    -- Clasificación de impacto
    CASE 
        WHEN COALESCE(gf.Porcentaje_Foco, 0) - COALESCE(uc.Porcentaje_Total, 0) > 5 THEN '📈 MAYOR EN FOCO'
        WHEN COALESCE(gf.Porcentaje_Foco, 0) - COALESCE(uc.Porcentaje_Total, 0) < -5 THEN '📉 MENOR EN FOCO'
        ELSE '🟰 SIMILAR'
    END as Comparacion
FROM GrupoFoco gf
FULL OUTER JOIN UniversoCompleto uc ON gf.Area_Derivacion = uc.Area_Derivacion
ORDER BY COALESCE(uc.Cantidad_Total, gf.Cantidad_Foco) DESC;

-- ===============================================================
-- ALTERNATIVA: FILTRO MÁS ESPECÍFICO POR TIPO DE ACTIVIDAD
-- (Si conoces los valores exactos, puedes usar esta versión)
-- ===============================================================
/*
-- Reemplaza el filtro anterior por este si conoces los valores exactos:
AND [Tipo de actividad] IN (
    'Alta de Servicio',
    'Alta Residencial', 
    'Migración de Tecnología',
    'Migración HFC a FTTH',
    -- Agrega aquí los valores exactos que necesites
)
*/



## 📊 Resultados de la Query Comparativa
Análisis: Actividades de ALTA y MIGRACIÓN - Junio 2025
Área de DerivaciónGrupo Foco (11 RUTs)% FocoUniverso Total% TotalDiferenciaComparaciónPlataforma Reagendamiento14365.60%23,94945.16%+20.44%📈 MAYOR EN FOCOComercial Venta3817.43%6,02511.36%+6.07%📈 MAYOR EN FOCOGestión de Infactibilidad2611.93%1,0972.07%+9.86%📈 MAYOR EN FOCOGSA41.83%2,0643.89%-2.06%🟰 SIMILARRedes Neutra41.83%1,5362.90%-1.07%🟰 SIMILARAlta Utilización20.92%550.10%+0.82%🟰 SIMILARRedes10.46%4250.80%-0.34%🟰 SIMILARClaro Plataforma Reagendamiento00.00%11,00220.75%-20.75%📉 MENOR EN FOCOClaro Comercial Venta00.00%5,32610.04%-10.04%📉 MENOR EN FOCOOtras áreas00.00%1,5492.92%-2.92%📉 MENOR EN FOCO

-- ===============================================================
-- QUERY ADICIONAL: VERIFICAR TIPOS DE ACTIVIDAD DISPONIBLES
-- ===============================================================
SELECT DISTINCT 
    [Tipo de actividad] as Tipo_Actividad,
    COUNT(*) as Total_Registros,
    SUM(CASE WHEN Estado = 'No Realizada' THEN 1 ELSE 0 END) as No_Realizadas,
    -- Verificar si contiene ALTA
    CASE WHEN UPPER([Tipo de actividad]) LIKE '%ALTA%' THEN '✅ ALTA' ELSE '' END as Contiene_Alta,
    -- Verificar si contiene MIGRA
    CASE WHEN UPPER([Tipo de actividad]) LIKE '%MIGRA%' THEN '✅ MIGRA' ELSE '' END as Contiene_Migra
FROM toa_BI_estatico 
WHERE YEAR(fecha_format) = 2025 
    AND MONTH(fecha_format) = 6
    AND [Tipo de actividad] IS NOT NULL 
    AND [Tipo de actividad] != ''
GROUP BY [Tipo de actividad]
ORDER BY Total_Registros DESC;
```


##  ===============================================================
##  🔍 QUERY ANÁLISIS DE TIPOS DE ACTIVIDAD DISPONIBLES
##  ===============================================================
```sql
SELECT DISTINCT 
    [Tipo de actividad] as Tipo_Actividad,
    COUNT(*) as Cantidad
FROM toa_BI_estatico 
WHERE YEAR(fecha_format) = 2025 
    AND MONTH(fecha_format) = 6
    AND RUT_FORMAT IN ('20189443-3', '13449606-1', '12631108-7', '19777151-8', 
                      '19485174-K', '20239971-1', '22064488-K', '16280795-1', 
                      '13091348-2', '13879192-0', '20187880-2')
    AND Estado = 'No Realizada'
    AND [Tipo de actividad] IS NOT NULL AND [Tipo de actividad] != ''
GROUP BY [Tipo de actividad]
ORDER BY Cantidad DESC;
```

---

## 3. Segmentación de Motivos - Código de Cierre

### Query Ejecutada:
```sql
SELECT 
    [Código de Cierre] as Codigo_Cierre,
    COUNT(*) as Cantidad
FROM toa_BI_estatico 
WHERE YEAR(fecha_format) = 2025 
    AND MONTH(fecha_format) = 6
    AND RUT_FORMAT IN ('20189443-3', '13449606-1', '12631108-7', '19777151-8', '19485174-K', '20239971-1', '22064488-K', '16280795-1', '13091348-2', '13879192-0', '20187880-2')
    AND Estado = 'No Realizada'
    AND [Código de Cierre] IS NOT NULL AND [Código de Cierre] != ''
GROUP BY [Código de Cierre]
ORDER BY Cantidad DESC
```

### Resultados: Segmentacion de altas y migracion 

| Código de Cierre | Cantidad | Porcentaje |
|------------------|----------|------------|
| **Sin moradores** | 108 | 44.26% |
| **Cliente reagenda** | 39 | 15.98% |
| **Clte desiste motivo comercial** | 21 | 8.61% |
| **Falla Masiva** | 18 | 7.38% |
| **Ductos tapados** | 16 | 6.56% |
| **Sin acceso a vivienda** | 8 | 3.28% |
| **Falta de dotación** | 7 | 2.87% |
| **Direccion incorrecta** | 6 | 2.46% |
| **Falla Aprovisionamiento** | 4 | 1.64% |
| **NAP Problema niveles PO** | 4 | 1.64% |
| **Zona Roja** | 4 | 1.64% |
| **Orden mal generada** | 3 | 1.23% |
| **Riesgo electrico** | 2 | 0.82% |
| **NAP Problema ubicación** | 2 | 0.82% |
| **Técnico sin estacionamiento** | 2 | 0.82% |
| **Otros motivos (7 códigos)** | 5 | 2.05% |

---

## 4. Análisis de Productividad Técnica - Días con 0 Actividades Completadas

### Query Ejecutada:
```sql
-- Query para identificar días sin actividades completadas por técnico
SELECT DISTINCT 
    Técnico,
    fecha_format,
    COUNT(*) as Total_Actividades,
    SUM(CASE WHEN Estado = 'Completado' THEN 1 ELSE 0 END) as Completadas,
    SUM(CASE WHEN Estado = 'No Realizada' THEN 1 ELSE 0 END) as No_Realizadas
FROM toa_BI_estatico 
WHERE YEAR(fecha_format) = 2025 
    AND MONTH(fecha_format) = 6
    AND RUT_FORMAT IN ('20189443-3', '13449606-1', '12631108-7', '19777151-8', '19485174-K', '20239971-1', '22064488-K', '16280795-1', '13091348-2', '13879192-0', '20187880-2')
GROUP BY Técnico, fecha_format
HAVING SUM(CASE WHEN Estado = 'Completado' THEN 1 ELSE 0 END) = 0
ORDER BY Técnico, fecha_format
```

### Query de Resumen:
```sql
SELECT 
    Técnico,
    COUNT(DISTINCT fecha_format) as Dias_Sin_Completadas
FROM toa_BI_estatico 
WHERE YEAR(fecha_format) = 2025 
    AND MONTH(fecha_format) = 6
    AND RUT_FORMAT IN ('20189443-3', '13449606-1', '12631108-7', '19777151-8', '19485174-K', '20239971-1', '22064488-K', '16280795-1', '13091348-2', '13879192-0', '20187880-2')
    AND Técnico IN (
        SELECT DISTINCT Técnico
        FROM toa_BI_estatico 
        WHERE YEAR(fecha_format) = 2025 
            AND MONTH(fecha_format) = 6
            AND RUT_FORMAT IN ('20189443-3', '13449606-1', '12631108-7', '19777151-8', '19485174-K', '20239971-1', '22064488-K', '16280795-1', '13091348-2', '13879192-0', '20187880-2')
        GROUP BY Técnico, fecha_format
        HAVING SUM(CASE WHEN Estado = 'Completado' THEN 1 ELSE 0 END) = 0
    )
GROUP BY Técnico
ORDER BY Dias_Sin_Completadas DESC
```

### Resultados:

| Técnico | Días sin Actividades Completadas |
|---------|----------------------------------|
| **NFTT_TQW_Juan Marin T** | 22 |
| **NFTT_TQW_Eduardo Espinoza** | 20 |
| **NFTT_TQW_Jesus Lepe R** | 20 |
| **NFTT_TQW_Jaime Munoz M** | 20 |
| **NFTT_TQW_Martin Aravena C** | 17 |
| **NFTT_TQW_Carlos Correa Valdes** | 17 |
| **NFTT_TQW_Ruperto Rojas J** | 17 |
| **NFTT_TQW_Luciano Aranda C** | 16 |
| **NFTT_TQW_Mario Perez Carrasco** | 12 |
| **NFTT_TQW_Nelson Mendez** | 7 |
| **NFTT_TQW_Victor Munoz Garrido** | 7 |
| **NFTT_TQW_Mario Perez Carrasco** | 3 |

---

## 5. Tabla Consolidada de Indicadores por RUT

### Query Base para Datos por RUT:
```sql
SELECT 
    RUT_FORMAT,
    COUNT(*) as Total_Actividades,
    SUM(CASE WHEN Estado = 'Completado' THEN 1 ELSE 0 END) as Completadas,
    SUM(CASE WHEN Estado = 'No Realizada' THEN 1 ELSE 0 END) as No_Realizadas,
    ROUND(SUM(CASE WHEN Estado = 'Completado' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as Porcentaje_Completadas,
    ROUND(SUM(CASE WHEN Estado = 'No Realizada' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as Porcentaje_No_Realizadas
FROM toa_BI_estatico 
WHERE YEAR(fecha_format) = 2025 
    AND MONTH(fecha_format) = 6
    AND RUT_FORMAT IN ('20189443-3', '13449606-1', '12631108-7', '19777151-8', '19485174-K', '20239971-1', '22064488-K', '16280795-1', '13091348-2', '13879192-0', '20187880-2')
GROUP BY RUT_FORMAT
ORDER BY RUT_FORMAT
```

### Tabla Consolidada de Todos los Indicadores (Con Información de Técnicos y Productividad por Días):

| RUT | Técnico Asignado | Total Act. | Comp. | % Comp. | No Real. | % No Real. | Días Trab. | Días PX | Días PX=0 | % Días Improd. | P.Reagend. | % P.R. | Com.Venta | % C.V. | G.Infact. | % G.I. | Otras | % Otras | Observaciones |
|-----|------------------|------------|-------|---------|----------|------------|------------|----------|-------------|------------------|------------|--------|-----------|--------|-----------|--------|-------|---------|---------------|
| **12631108-7** | Jaime Muñoz M. | 37 | 11 | 29.73% | 20 | 54.05% | 20 | 10 | 10 | **50.0%** 🔴 | 14 | 70.0% | 3 | 15.0% | 0 | 0.0% | 3 | 15.0% | Redes Neutra (3) |
| **13091348-2** | **Carlos Enrique Correa Valdés** | 36 | 12 | 33.33% | 18 | 50.0% | 17 | 10 | 7 | **41.2%** 🟢 | 10 | 55.56% | 5 | 27.78% | 1 | 5.56% | 2 | 11.1% | Redes Neutra (2) |
| **13449606-1** | Víctor Muñoz Garrido | 29 | 5 | 17.24% | 16 | 55.17% | 7 | 4 | 3 | **42.9%** 🟢 | 12 | 75.0% | 2 | 12.5% | 1 | 6.25% | 1 | 6.25% | Backoffice (1) |
| **13879192-0** | Juan Marín T. | 61 | 13 | 21.31% | 26 | 42.62% | 22 | 11 | 11 | **50.0%** 🔴 | 19 | 73.08% | 4 | 15.38% | 3 | 11.54% | 0 | 0.0% | - |
| **16280795-1** | Mario Pérez Carrasco | 33 | 9 | 27.27% | 17 | 51.52% | 15 | 8 | 7 | **46.7%** 🟡 | 11 | 64.71% | 1 | 5.88% | 5 | 29.41% | 0 | 0.0% | - |
| **19485174-K** | **Luciano Andrés Aranda Castillo** | 56 | 16 | 28.57% | 18 | 32.14% | 23 | 12 | 11 | **47.8%** 🟡 | 13 | 72.22% | 3 | 16.67% | 1 | 5.56% | 1 | 5.55% | Redes (1) |
| **19777151-8** | **Ruperto Ignacio Rojas Jiménez** | 73 | 26 | 35.62% | 31 | 42.47% | 19 | 10 | 9 | **47.4%** 🟡 | 21 | 67.74% | 5 | 16.13% | 2 | 6.45% | 3 | 9.68% | R.Neutra(1), Backoffice(1), GSA(1) |
| **20187880-2** | Nelson Méndez | 35 | 15 | 42.86% | 12 | 34.29% | 15 | 10 | 5 | **33.3%** 🟢 | 9 | 75.0% | 1 | 8.33% | 1 | 8.33% | 1 | 8.34% | Redes Neutra (1) |
| **20189443-3** | **Martín Alonso Ismael Aravena Contreras** | 42 | 13 | 30.95% | 27 | 64.29% | 17 | 9 | 8 | **47.1%** 🟡 | 21 | 77.78% | 2 | 7.41% | 1 | 3.7% | 3 | 11.11% | GSA(2), Alta Utilización(1) |
| **20239971-1** | **Eduardo Andrés Vicencio Espinoza** | 51 | 11 | 21.57% | 23 | 45.1% | 20 | 8 | 12 | **60.0%** 🔴 | 19 | 82.61% | 1 | 4.35% | 2 | 8.7% | 1 | 4.34% | Redes Neutra (1) |
| **22064488-K** | **Jesús Ignacio Lepe Rojas** | 70 | 23 | 32.86% | 41 | 58.57% | 20 | 15 | 5 | **25.0%** 🟢 | 19 | 46.34% | 11 | 26.83% | 9 | 21.95% | 2 | 4.88% | GSA(1), Alta Utilización(1) |

### 🚨 Alertas Críticas por % Días Improductivos:

| Nivel de Alerta | Técnico | % Días Improductivos | Impacto |
|----------------|----------|----------------------|----------|
| 🔴 **CRÍTICO** | Eduardo Espinoza | **60.0%** | 12 de 20 días |
| 🔴 **CRÍTICO** | Jaime Muñoz M | **50.0%** | 10 de 20 días |
| 🔴 **CRÍTICO** | Juan Marín T | **50.0%** | 11 de 22 días |
| 🟡 **MEDIO** | Luciano Aranda | **47.8%** | 11 de 23 días |
| 🟡 **MEDIO** | Ruperto Rojas | **47.4%** | 9 de 19 días |
| 🟡 **MEDIO** | Martín Aravena | **47.1%** | 8 de 17 días |
| 🟡 **MEDIO** | Mario Pérez | **46.7%** | 7 de 15 días |
| 🟢 **ACEPTABLE** | Víctor Muñoz | **42.9%** | 3 de 7 días |
| 🟢 **ACEPTABLE** | Carlos Correa | **41.2%** | 7 de 17 días |
| 🟢 **BUENO** | Nelson Méndez | **33.3%** | 5 de 15 días |
| 🟢 **EXCELENTE** | Jesús Lepe | **25.0%** | 5 de 20 días |

### Información Detallada de Técnicos:

| Técnico | Nombre Completo | Supervisor | Área | RUTs Asignados | Total Actividades |
|---------|------------------|-------------|------|----------------|-------------------|
| **Carlos Correa V.** | Carlos Enrique Correa Valdés | GUERRERO | Operaciones ZMET Residencial | 13091348-2 | 36 |
| **Eduardo Espinoza** | Eduardo Andrés Vicencio Espinoza | CORROTEA | Operaciones ZCEN Residencial | 20239971-1 | 51 |
| **Jesús Lepe R.** | Jesús Ignacio Lepe Rojas | GOMEZ | Operaciones ZMET Residencial | 22064488-K | 70 |
| **Luciano Aranda C.** | Luciano Andrés Aranda Castillo | GUERRERO | Operaciones ZMET Res | 19485174-K | 56 |
| **Martín Aravena C.** | Martín Alonso Ismael Aravena Contreras | ARJONA | Operaciones ZMET Residencial | 20189443-3 | 42 |
| **Ruperto Rojas J.** | Ruperto Ignacio Rojas Jiménez | CORROTEA | Operaciones ZCEN Res | 19777151-8 | 73 |
| **Jaime Muñoz M.** | *No encontrado en tb_user_tqw* | - | - | 12631108-7 | 37 |
| **Juan Marín T.** | *No encontrado en tb_user_tqw* | - | - | 13879192-0 | 61 |
| **Mario Pérez C.** | *No encontrado en tb_user_tqw* | - | - | 16280795-1 | 33 |
| **Nelson Méndez** | *No encontrado en tb_user_tqw* | - | - | 20187880-2 | 35 |
| **Víctor Muñoz G.** | *No encontrado en tb_user_tqw* | - | - | 13449606-1 | 29 |

### Resumen de la Tabla Consolidada:

**Mejores Rendimientos:**
- **Mayor % Completadas:** 20187880-2 (42.86%)
- **Menor % No Realizadas:** 19485174-K (32.14%)
- **Mayor Volumen Actividades:** 19777151-8 (73 actividades)

**Rendimientos Críticos:**
- **Menor % Completadas:** 13449606-1 (17.24%)
- **Mayor % No Realizadas:** 20189443-3 (64.29%)
- **Mayor dependencia P.Reagendamiento:** 20239971-1 (82.61%)

**Patrones Identificados:**
- Todos los RUTs muestran alta dependencia de Plataforma Reagendamiento (46%-83%)
- RUT 22064488-K presenta el mayor balance entre áreas de derivación
- RUT 16280795-1 tiene la mayor proporción en Gestión de Infactibilidad (29.41%)

---

## 6. Conclusiones y Recomendaciones

### Hallazgos Principales:

1. **Baja Tasa de Completación:** Solo el 38.21% de las actividades fueron completadas exitosamente.

2. **Principal Motivo de Derivación:** El 67.47% de las actividades no realizadas se derivan a "Plataforma Reagendamiento".

3. **Principal Código de Cierre:** "Sin moradores" representa el 44.26% de los motivos de no realización.

4. **Problemas de Productividad por Técnico:** Varios técnicos presentan un alto número de días sin actividades completadas. Juan Marín T es el más afectado con 22 días, seguido por Eduardo Espinoza, Jesús Lepe y Jaime Muñoz con 20 días cada uno.

5. **Análisis por Supervisor:** Los supervisores CORROTEA y GUERRERO tienen técnicos con problemas de productividad significativos.

### Recomendaciones:

1. **Mejorar Contactabilidad:** Implementar estrategias para reducir el motivo "Sin moradores" (44.26%).

2. **Optimizar Reagendamiento:** Revisar el proceso de la Plataforma de Reagendamiento que maneja el 67.47% de las derivaciones.

3. **Plan de Mejora Técnica:** Establecer un plan de acción para los técnicos con mayor número de días improductivos.

4. **Validación de Datos:** Revisar la calidad de la información de contacto y direcciones para reducir "Direccion incorrecta".

---

## 7. Análisis Transversal de Técnicos

### Query para Cruze con tb_user_tqw:
```sql
SELECT 
    bi.Técnico,
    u.nombre as Nombre_Completo,
    u.Nombre_short,
    u.area as Area_Tecnico,
    u.supervisor
FROM (
    SELECT DISTINCT Técnico 
    FROM toa_BI_estatico 
    WHERE YEAR(fecha_format) = 2025 AND MONTH(fecha_format) = 6
        AND RUT_FORMAT IN ('20189443-3', '13449606-1', '12631108-7', '19777151-8', 
                          '19485174-K', '20239971-1', '22064488-K', '16280795-1', 
                          '13091348-2', '13879192-0', '20187880-2')
) bi
LEFT JOIN tb_user_tqw u ON (
    (bi.Técnico LIKE '%Carlos Correa%' AND u.nombre LIKE '%Carlos Enrique Correa Valdés%') OR
    (bi.Técnico LIKE '%Eduardo Espinoza%' AND u.nombre LIKE '%Eduardo Andrés Vicencio Espinoza%') OR
    (bi.Técnico LIKE '%Jesus Lepe%' AND u.nombre LIKE '%Jesús  Ignacio Lepe Rojas%') OR
    (bi.Técnico LIKE '%Luciano Aranda%' AND u.nombre LIKE '%Luciano Andres Aranda Castillo%') OR
    (bi.Técnico LIKE '%Martin Aravena%' AND u.nombre LIKE '%Martín Alonso Ismael Aravena Contreras%') OR
    (bi.Técnico LIKE '%Ruperto Rojas%' AND u.nombre LIKE '%Ruperto Ignacio Rojas  Jiménez%')
)
WHERE u.vigente = 'SI' OR u.vigente IS NULL
```

### Resumen Transversal de Técnicos por RUT:

| RUT | Técnico Asignado | Nombre Completo | Supervisor | Área | Act. Total | % Completadas | Días Improductivos |
|-----|------------------|------------------|-------------|------|------------|---------------|--------------------|
| **12631108-7** | Jaime Muñoz M. | *No identificado* | - | - | 37 | 29.73% | 20 |
| **13091348-2** | Carlos Correa V. | Carlos Enrique Correa Valdés | GUERRERO | ZMET Residencial | 36 | 33.33% | 17 |
| **13449606-1** | Víctor Muñoz G. | *No identificado* | - | - | 29 | 17.24% | 7 |
| **13879192-0** | Juan Marín T. | *No identificado* | - | - | 61 | 21.31% | 22 |
| **16280795-1** | Mario Pérez C. | *No identificado* | - | - | 33 | 27.27% | 12 |
| **19485174-K** | Luciano Aranda C. | Luciano Andrés Aranda Castillo | GUERRERO | ZMET Res | 56 | 28.57% | 16 |
| **19777151-8** | Ruperto Rojas J. | Ruperto Ignacio Rojas Jiménez | CORROTEA | ZCEN Res | 73 | 35.62% | 17 |
| **20187880-2** | Nelson Méndez | *No identificado* | - | - | 35 | 42.86% | 7 |
| **20189443-3** | Martín Aravena C. | Martín Alonso Ismael Aravena Contreras | ARJONA | ZMET Residencial | 42 | 30.95% | 17 |
| **20239971-1** | Eduardo Espinoza | Eduardo Andrés Vicencio Espinoza | CORROTEA | ZCEN Residencial | 51 | 21.57% | 20 |
| **22064488-K** | Jesús Lepe R. | Jesús Ignacio Lepe Rojas | GOMEZ | ZMET Residencial | 70 | 32.86% | 20 |

### 📈 Insights Clave por Supervisor:

#### CORROTEA (Zona Centro)
- **Técnicos:** Eduardo Espinoza, Ruperto Rojas
- **Rendimiento promedio:** 28.6% completadas
- **Alertas:** Eduardo con 20 días improductivos (67% del mes)
- **Fortaleza:** Ruperto con mayor volumen (73 actividades)

#### GUERRERO (Zona Metropolitana)
- **Técnicos:** Carlos Correa, Luciano Aranda
- **Rendimiento promedio:** 30.9% completadas
- **Balance:** Ambos técnicos con productividad similar
- **Oportunidad:** Optimizar días improductivos (16-17 días)

#### GOMEZ & ARJONA (Individual)
- **GOMEZ:** Jesús Lepe - 32.86% completadas, 20 días improductivos
- **ARJONA:** Martín Aravena - 30.95% completadas, 17 días improductivos

#### 🔴 Técnicos Sin Identificar (5 personas)
- **Impacto:** 195 actividades totales
- **Riesgo:** No se puede realizar seguimiento personalizado
- **Acción:** Actualizar tb_user_tqw con información faltante

---

## 8. Query Completa para Tabla Consolidada de Indicadores

### Query Maestra para Reproducción del Análisis:

```sql
-- QUERY CONSOLIDADA COMPLETA PARA TABLA DE INDICADORES POR RUT
-- Incluye: Actividades, Productividad por Días, Segmentación por Área
-- QUERY CONSOLIDADA COMPLETA: PRODUCTIVIDAD JUNIO + CALIDAD REACTIVA MAYO
-- Combina análisis de actividades de Junio 2025 con Calidad Reactiva de Mayo 2025
-- Incluye: Actividades, Productividad por Días, Segmentación por Área + Calidad Reactiva

WITH ResumenActividades AS (
    -- Resumen de actividades por RUT (Junio 2025)
    SELECT 
        RUT_FORMAT,
        COUNT(*) as Total_Actividades,
        SUM(CASE WHEN Estado = 'Completado' THEN 1 ELSE 0 END) as Completadas,
        SUM(CASE WHEN Estado = 'No Realizada' THEN 1 ELSE 0 END) as No_Realizadas,
        ROUND(SUM(CASE WHEN Estado = 'Completado' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as Porcentaje_Completadas,
        ROUND(SUM(CASE WHEN Estado = 'No Realizada' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as Porcentaje_No_Realizadas
    FROM toa_BI_estatico 
    WHERE YEAR(fecha_format) = 2025 
        AND MONTH(fecha_format) = 6
        AND RUT_FORMAT IN ('20189443-3', '13449606-1', '12631108-7', '19777151-8', 
                          '19485174-K', '20239971-1', '22064488-K', '16280795-1', 
                          '13091348-2', '13879192-0', '20187880-2')
    GROUP BY RUT_FORMAT
),
ProductividadDias AS (
    -- Análisis de productividad por días (Junio 2025)
    SELECT 
        RUT_FORMAT,
        CASE 
            WHEN Técnico LIKE '%Carlos Correa%' THEN 'Carlos Enrique Correa Valdés'
            WHEN Técnico LIKE '%Eduardo Espinoza%' THEN 'Eduardo Andrés Vicencio Espinoza'
            WHEN Técnico LIKE '%Jaime Munoz%' THEN 'Jaime Muñoz M'
            WHEN Técnico LIKE '%Jesus Lepe%' THEN 'Jesús Ignacio Lepe Rojas'
            WHEN Técnico LIKE '%Juan Marin%' THEN 'Juan Marín T'
            WHEN Técnico LIKE '%Luciano Aranda%' THEN 'Luciano Andrés Aranda Castillo'
            WHEN Técnico LIKE '%Mario Perez%' OR Técnico LIKE '%Mario  Perez%' THEN 'Mario Pérez Carrasco'
            WHEN Técnico LIKE '%Martin Aravena%' THEN 'Martín Alonso Ismael Aravena Contreras'
            WHEN Técnico LIKE '%Nelson Mendez%' THEN 'Nelson Méndez'
            WHEN Técnico LIKE '%Ruperto Rojas%' THEN 'Ruperto Ignacio Rojas Jiménez'
            WHEN Técnico LIKE '%Victor Munoz%' THEN 'Víctor Muñoz Garrido'
            ELSE Técnico
        END as Tecnico_Consolidado,
        COUNT(DISTINCT fecha_format) as Total_Dias_Trabajados,
        COUNT(DISTINCT CASE WHEN Estado = 'Completado' THEN fecha_format END) as Dias_Con_Productividad
    FROM toa_BI_estatico 
    WHERE YEAR(fecha_format) = 2025 
        AND MONTH(fecha_format) = 6
        AND RUT_FORMAT IN ('20189443-3', '13449606-1', '12631108-7', '19777151-8', 
                          '19485174-K', '20239971-1', '22064488-K', '16280795-1', 
                          '13091348-2', '13879192-0', '20187880-2')
    GROUP BY RUT_FORMAT, 
        CASE 
            WHEN Técnico LIKE '%Carlos Correa%' THEN 'Carlos Enrique Correa Valdés'
            WHEN Técnico LIKE '%Eduardo Espinoza%' THEN 'Eduardo Andrés Vicencio Espinoza'
            WHEN Técnico LIKE '%Jaime Munoz%' THEN 'Jaime Muñoz M'
            WHEN Técnico LIKE '%Jesus Lepe%' THEN 'Jesús Ignacio Lepe Rojas'
            WHEN Técnico LIKE '%Juan Marin%' THEN 'Juan Marín T'
            WHEN Técnico LIKE '%Luciano Aranda%' THEN 'Luciano Andrés Aranda Castillo'
            WHEN Técnico LIKE '%Mario Perez%' OR Técnico LIKE '%Mario  Perez%' THEN 'Mario Pérez Carrasco'
            WHEN Técnico LIKE '%Martin Aravena%' THEN 'Martín Alonso Ismael Aravena Contreras'
            WHEN Técnico LIKE '%Nelson Mendez%' THEN 'Nelson Méndez'
            WHEN Técnico LIKE '%Ruperto Rojas%' THEN 'Ruperto Ignacio Rojas Jiménez'
            WHEN Técnico LIKE '%Victor Munoz%' THEN 'Víctor Muñoz Garrido'
            ELSE Técnico
        END
),
SegmentacionAreas AS (
    -- Segmentación por áreas de derivación (Junio 2025)
    SELECT 
        RUT_FORMAT,
        SUM(CASE WHEN [Area derivación] = 'Plataforma Reagendamiento' THEN 1 ELSE 0 END) as Plataforma_Reagendamiento,
        SUM(CASE WHEN [Area derivación] = 'Comercial Venta' THEN 1 ELSE 0 END) as Comercial_Venta,
        SUM(CASE WHEN [Area derivación] = 'Gestión de Infactibilidad' THEN 1 ELSE 0 END) as Gestion_Infactibilidad,
        SUM(CASE WHEN [Area derivación] NOT IN ('Plataforma Reagendamiento', 'Comercial Venta', 'Gestión de Infactibilidad') 
                 AND [Area derivación] IS NOT NULL AND [Area derivación] != '' THEN 1 ELSE 0 END) as Otras_Areas
    FROM toa_BI_estatico 
    WHERE YEAR(fecha_format) = 2025 
        AND MONTH(fecha_format) = 6
        AND RUT_FORMAT IN ('20189443-3', '13449606-1', '12631108-7', '19777151-8', 
                          '19485174-K', '20239971-1', '22064488-K', '16280795-1', 
                          '13091348-2', '13879192-0', '20187880-2')
        AND Estado = 'No Realizada'
    GROUP BY RUT_FORMAT
),
CalidadReactiva AS (
    -- Calidad Reactiva consolidada (Mayo 2025)
    SELECT 
        kpi.RUT_TECNICO,
        u.nombre as Nombre_Tecnico_Calidad,
        u.supervisor,
        u.area,
        kpi.Q_Calidad30_Total as Total_Trabajos_Calidad,
        kpi.Q_Cantidad_Total as Total_Trabajos_Realizados,
        ROUND(kpi.Eficiencia_General, 2) as Calidad_Reactiva_Consolidada,
        CASE 
            WHEN kpi.Eficiencia_General >= 98 THEN 'EXCELENTE'
            WHEN kpi.Eficiencia_General >= 96 THEN 'BUENO'
            WHEN kpi.Eficiencia_General >= 94 THEN 'ACEPTABLE'
            WHEN kpi.Eficiencia_General >= 90 THEN 'BAJO'
            ELSE 'CRÍTICO'
        END as Clasificacion_Calidad,
        -- Detalles por tipo de red
        kpi.Q_Calidad30_FTTH,
        kpi.Q_Cantidad_FTTH,
        ROUND(kpi.Eficiencia_FTTH, 2) as Eficiencia_FTTH,
        kpi.Q_Calidad30_HFC,
        kpi.Q_Cantidad_HFC,
        ROUND(kpi.Eficiencia_HFC, 2) as Eficiencia_HFC
    FROM vw_CalidadFlujo_kpi kpi
    INNER JOIN tb_user_tqw u ON kpi.RUT_TECNICO = u.rut
    WHERE kpi.MES_CONTABLE = '2025-05-01'
        AND kpi.RUT_TECNICO IN (
            '19777151-8',   -- Ruperto Ignacio Rojas Jiménez
            '19485174-K',   -- Luciano Andres Aranda Castillo  
            '22064488-K',   -- Jesús Ignacio Lepe Rojas
            '20189443-3',   -- Martín Alonso Ismael Aravena Contreras
            '20239971-1',   -- Eduardo Andrés Vicencio Espinoza
            '13091348-2'    -- Carlos Enrique Correa Valdés
        )
        AND u.vigente = 'Si'
)

-- QUERY PRINCIPAL: TABLA CONSOLIDADA COMPLETA
SELECT 
    -- IDENTIFICACIÓN
    ra.RUT_FORMAT,
    pd.Tecnico_Consolidado,
    cr.supervisor,
    cr.area,
    
    -- PRODUCTIVIDAD JUNIO 2025
    ra.Total_Actividades,
    ra.Completadas,
    ra.Porcentaje_Completadas,
    ra.No_Realizadas,
    ra.Porcentaje_No_Realizadas,
    
    -- PRODUCTIVIDAD POR DÍAS JUNIO 2025
    pd.Total_Dias_Trabajados,
    pd.Dias_Con_Productividad,
    (pd.Total_Dias_Trabajados - pd.Dias_Con_Productividad) as Dias_Sin_Productividad,
    ROUND(((pd.Total_Dias_Trabajados - pd.Dias_Con_Productividad) * 100.0 / pd.Total_Dias_Trabajados), 1) as Porcentaje_Dias_Improductivos,
    
    -- CLASIFICACIÓN PRODUCTIVIDAD
    CASE 
        WHEN ((pd.Total_Dias_Trabajados - pd.Dias_Con_Productividad) * 100.0 / pd.Total_Dias_Trabajados) >= 50 THEN '🔴 CRÍTICO'
        WHEN ((pd.Total_Dias_Trabajados - pd.Dias_Con_Productividad) * 100.0 / pd.Total_Dias_Trabajados) >= 45 THEN '🟡 MEDIO'
        WHEN ((pd.Total_Dias_Trabajados - pd.Dias_Con_Productividad) * 100.0 / pd.Total_Dias_Trabajados) >= 35 THEN '🟢 ACEPTABLE'
        ELSE '🟢 BUENO'
    END as Alerta_Productividad,
    
    -- SEGMENTACIÓN ÁREAS JUNIO 2025
    sa.Plataforma_Reagendamiento,
    ROUND((sa.Plataforma_Reagendamiento * 100.0 / NULLIF(ra.No_Realizadas, 0)), 2) as Porcentaje_Plataforma,
    sa.Comercial_Venta,
    ROUND((sa.Comercial_Venta * 100.0 / NULLIF(ra.No_Realizadas, 0)), 2) as Porcentaje_Comercial,
    sa.Gestion_Infactibilidad,
    ROUND((sa.Gestion_Infactibilidad * 100.0 / NULLIF(ra.No_Realizadas, 0)), 2) as Porcentaje_Gestion,
    sa.Otras_Areas,
    ROUND((sa.Otras_Areas * 100.0 / NULLIF(ra.No_Realizadas, 0)), 2) as Porcentaje_Otras,
    
    -- CALIDAD REACTIVA MAYO 2025
    COALESCE(cr.Calidad_Reactiva_Consolidada, 0) as Calidad_Reactiva_Mayo,
    COALESCE(cr.Clasificacion_Calidad, 'SIN DATOS') as Clasificacion_Calidad,
    COALESCE(cr.Total_Trabajos_Calidad, 0) as Trabajos_Calidad_Mayo,
    COALESCE(cr.Total_Trabajos_Realizados, 0) as Trabajos_Realizados_Mayo,
    
    -- DETALLES CALIDAD POR TIPO RED
    COALESCE(cr.Q_Calidad30_FTTH, 0) as Calidad_FTTH_Mayo,
    COALESCE(cr.Q_Cantidad_FTTH, 0) as Cantidad_FTTH_Mayo,
    COALESCE(cr.Eficiencia_FTTH, 0) as Eficiencia_FTTH_Mayo,
    COALESCE(cr.Q_Calidad30_HFC, 0) as Calidad_HFC_Mayo,
    COALESCE(cr.Q_Cantidad_HFC, 0) as Cantidad_HFC_Mayo,
    COALESCE(cr.Eficiencia_HFC, 0) as Eficiencia_HFC_Mayo,
    
    -- ANÁLISIS CORRELACIONAL
    CASE 
        WHEN COALESCE(cr.Calidad_Reactiva_Consolidada, 0) >= 98 AND ra.Porcentaje_Completadas >= 35 THEN '🟢 EXCELENTE INTEGRAL'
        WHEN COALESCE(cr.Calidad_Reactiva_Consolidada, 0) >= 98 AND ra.Porcentaje_Completadas < 35 THEN '🟡 ALTA CALIDAD, BAJA PRODUCTIVIDAD'
        WHEN COALESCE(cr.Calidad_Reactiva_Consolidada, 0) < 96 AND ra.Porcentaje_Completadas >= 35 THEN '🟡 ALTA PRODUCTIVIDAD, BAJA CALIDAD'
        WHEN COALESCE(cr.Calidad_Reactiva_Consolidada, 0) < 96 AND ra.Porcentaje_Completadas < 35 THEN '🔴 CRÍTICO INTEGRAL'
        WHEN cr.Calidad_Reactiva_Consolidada IS NULL THEN '⚪ SIN DATOS CALIDAD'
        ELSE '🟢 BALANCE ACEPTABLE'
    END as Evaluacion_Integral,
    
    -- PRIORIDAD DE INTERVENCIÓN
    CASE 
        WHEN COALESCE(cr.Calidad_Reactiva_Consolidada, 96) < 94 AND ra.Porcentaje_Completadas < 25 THEN 'URGENTE'
        WHEN COALESCE(cr.Calidad_Reactiva_Consolidada, 96) < 96 OR ra.Porcentaje_Completadas < 30 THEN 'ALTA'
        WHEN ((pd.Total_Dias_Trabajados - pd.Dias_Con_Productividad) * 100.0 / pd.Total_Dias_Trabajados) >= 50 THEN 'MEDIA'
        ELSE 'BAJA'
    END as Prioridad_Intervencion

FROM ResumenActividades ra
LEFT JOIN ProductividadDias pd ON ra.RUT_FORMAT = pd.RUT_FORMAT
LEFT JOIN SegmentacionAreas sa ON ra.RUT_FORMAT = sa.RUT_FORMAT
LEFT JOIN CalidadReactiva cr ON ra.RUT_FORMAT = cr.RUT_TECNICO

-- ORDENAMIENTO: Prioridad de intervención, luego por calidad y productividad
ORDER BY 
    CASE 
        WHEN COALESCE(cr.Calidad_Reactiva_Consolidada, 96) < 94 AND ra.Porcentaje_Completadas < 25 THEN 1
        WHEN COALESCE(cr.Calidad_Reactiva_Consolidada, 96) < 96 OR ra.Porcentaje_Completadas < 30 THEN 2
        WHEN ((pd.Total_Dias_Trabajados - pd.Dias_Con_Productividad) * 100.0 / pd.Total_Dias_Trabajados) >= 50 THEN 3
        ELSE 4
    END,
    COALESCE(cr.Calidad_Reactiva_Consolidada, 0) DESC,
    ra.Porcentaje_Completadas DESC;
```



## 9. Análisis de Calidad Reactiva - Mayo 2025

### 📊 Metodología de Cálculo de Calidad Reactiva

**Fuente de Datos:** `vw_CalidadFlujo_kpi` (SQL Server)
**Período Analizado:** Mayo 2025 (MES_CONTABLE = '2025-05-01')
**Grupo Foco:** 6 técnicos identificados en el análisis de actividades de junio

### 🎯 Definición de Calidad Reactiva Consolidada

La **Calidad Reactiva Consolidada** se calcula como:

```
Calidad_Reactiva = (Total_Trabajos_Calidad / Total_Trabajos_Realizados) × 100
```

**Donde:**
- `Total_Trabajos_Calidad`: Suma de trabajos que cumplen estándar de calidad (FTTH + HFC)
- `Total_Trabajos_Realizados`: Total de trabajos ejecutados en el período
- **No se segmenta por tipo de red** (como solicita el análisis)

### 📈 Query de Calidad Reactiva para el Grupo Foco:

```sql
-- Calidad Reactiva Consolidada Mayo 2025 - Grupo Foco Técnicos
SELECT 
    kpi.RUT_TECNICO,
    u.nombre as Nombre_Tecnico,
    u.supervisor,
    u.area,
    kpi.MES_CONTABLE,
    
    -- Totales de calidad (consolidados, no segmentados por tipo red)
    kpi.Q_Calidad30_Total as Total_Trabajos_Calidad,
    kpi.Q_Cantidad_Total as Total_Trabajos_Realizados,
    
    -- Calidad Reactiva Consolidada (metodología estándar)
    ROUND(kpi.Eficiencia_General, 2) as Calidad_Reactiva_Consolidada,
    
    -- Detalles por tipo de red (solo para análisis)
    kpi.Q_Calidad30_FTTH,
    kpi.Q_Cantidad_FTTH,
    ROUND(kpi.Eficiencia_FTTH, 2) as Eficiencia_FTTH,
    kpi.Q_Calidad30_HFC,
    kpi.Q_Cantidad_HFC,
    ROUND(kpi.Eficiencia_HFC, 2) as Eficiencia_HFC,
    
    -- Cálculo manual para verificación
    ROUND((CAST(kpi.Q_Calidad30_Total AS FLOAT) / NULLIF(kpi.Q_Cantidad_Total, 0)) * 100, 2) as Calidad_Reactiva_Manual,
    
    -- Clasificación de calidad
    CASE 
        WHEN kpi.Eficiencia_General >= 98 THEN 'EXCELENTE'
        WHEN kpi.Eficiencia_General >= 96 THEN 'BUENO'
        WHEN kpi.Eficiencia_General >= 94 THEN 'ACEPTABLE'
        WHEN kpi.Eficiencia_General >= 90 THEN 'BAJO'
        ELSE 'CRÍTICO'
    END as Clasificacion_Calidad

FROM vw_CalidadFlujo_kpi kpi
INNER JOIN tb_user_tqw u ON kpi.RUT_TECNICO = u.rut
WHERE kpi.MES_CONTABLE = '2025-05-01'
    AND kpi.RUT_TECNICO IN (
        '19777151-8',   -- Ruperto Ignacio Rojas Jiménez
        '19485174-K',   -- Luciano Andres Aranda Castillo  
        '22064488-K',   -- Jesús Ignacio Lepe Rojas
        '20189443-3',   -- Martín Alonso Ismael Aravena Contreras
        '20239971-1',   -- Eduardo Andrés Vicencio Espinoza
        '13091348-2'    -- Carlos Enrique Correa Valdés
    )
    AND u.vigente = 'Si'
ORDER BY kpi.Eficiencia_General DESC, u.supervisor, u.nombre
```

---

### 🏆 Resultados de Calidad Reactiva - Mayo 2025

| RUT Técnico | Nombre | Supervisor | Área | Trabajos Calidad | Trabajos Total | **Calidad Reactiva** | Clasificación | Observaciones |
|-------------|--------|------------|------|------------------|----------------|---------------------|---------------|---------------|
| **20189443-3** | Martín Alonso Ismael Aravena Contreras | ARJONA | ZMET Residencial | 28 | 28 | **100.00%** | 🟢 EXCELENTE | 26 FTTH + 2 HFC |
| **22064488-K** | Jesús Ignacio Lepe Rojas | GOMEZ | ZMET Residencial | 23 | 23 | **100.00%** | 🟢 EXCELENTE | 21 FTTH + 2 HFC |
| **13091348-2** | Carlos Enrique Correa Valdés | GUERRERO | ZMET Residencial | 4 | 4 | **100.00%** | 🟢 EXCELENTE | Solo FTTH |
| **19777151-8** | Ruperto Ignacio Rojas Jiménez | CORROTEA | ZCEN Res | 48 | 49 | **97.96%** | 🟡 BUENO | Solo HFC |
| **19485174-K** | Luciano Andres Aranda Castillo | GUERRERO | ZMET Res | 31 | 33 | **93.94%** | 🔴 BAJO | Solo HFC |
| **20239971-1** | Eduardo Andrés Vicencio Espinoza | CORROTEA | ZCEN Residencial | 42 | 46 | **91.30%** | 🔴 BAJO | Solo HFC |

---

### 📊 Resumen Estadístico del Grupo - Mayo 2025

| Métrica | Valor | Interpretación |
|---------|-------|----------------|
| **Total Técnicos Analizados** | 6 | Grupo foco completo |
| **Promedio Calidad Grupo** | **97.20%** | Sobre meta estándar (96%) |
| **Calidad Mínima** | 91.30% | Eduardo Espinoza |
| **Calidad Máxima** | 100.00% | 3 técnicos perfectos |
| **Total Trabajos Grupo** | 183 | Volumen significativo |
| **Total Trabajos Calidad** | 176 | 96.17% consolidado |
| **Técnicos EXCELENTE (≥98%)** | 3 | 50% del grupo |
| **Técnicos BUENO (96-97.9%)** | 1 | 16.7% del grupo |
| **Técnicos BAJO (90-95.9%)** | 2 | 33.3% del grupo |
| **Técnicos CRÍTICO (<90%)** | 0 | Sin casos críticos |

---

### 🎯 Análisis Comparativo: Productividad vs Calidad

| Técnico | Calidad Mayo (%) | % Completadas Junio | Días Improductivos Junio | Correlación |
|---------|------------------|---------------------|--------------------------|-------------|
| **Martín Aravena** | 100.00% | 30.95% | 47.1% | 🔴 Alta calidad, baja productividad |
| **Jesús Lepe** | 100.00% | 32.86% | 25.0% | 🟢 Mejor balance general |
| **Carlos Correa** | 100.00% | 33.33% | 41.2% | 🟡 Excelente calidad, productividad media |
| **Ruperto Rojas** | 97.96% | 35.62% | 47.4% | 🟡 Buen balance calidad-productividad |
| **Luciano Aranda** | 93.94% | 28.57% | 47.8% | 🔴 Bajo en ambos indicadores |
| **Eduardo Espinoza** | 91.30% | 21.57% | 60.0% | 🔴 Crítico en ambos indicadores |

---

### 🚨 Alertas Críticas por Calidad Reactiva

#### 🔴 ATENCIÓN INMEDIATA:
- **Eduardo Espinoza:** 91.30% - Bajo umbral meta (96%) + Mayor días improductivos
- **Luciano Aranda:** 93.94% - Cerca del límite + Productividad inconsistente

#### 🟡 MONITOREO:
- **Ruperto Rojas:** 97.96% - Buena calidad pero alta improductividad en junio

#### 🟢 CASOS EXITOSOS:
- **Martín, Jesús, Carlos:** 100% calidad - Replicar metodología

---

### 📋 Query Resumen Estadístico:

```sql
-- Resumen Estadístico Calidad Reactiva Grupo - Mayo 2025
SELECT 
    COUNT(*) as Total_Tecnicos,
    ROUND(AVG(kpi.Eficiencia_General), 2) as Promedio_Calidad_Grupo,
    ROUND(MIN(kpi.Eficiencia_General), 2) as Minima_Calidad,
    ROUND(MAX(kpi.Eficiencia_General), 2) as Maxima_Calidad,
    SUM(kpi.Q_Calidad30_Total) as Total_Trabajos_Calidad_Grupo,
    SUM(kpi.Q_Cantidad_Total) as Total_Trabajos_Realizados_Grupo,
    ROUND((CAST(SUM(kpi.Q_Calidad30_Total) AS FLOAT) / SUM(kpi.Q_Cantidad_Total)) * 100, 2) as Calidad_Reactiva_Consolidada_Grupo,
    SUM(CASE WHEN kpi.Eficiencia_General >= 98 THEN 1 ELSE 0 END) as Tecnicos_Excelente,
    SUM(CASE WHEN kpi.Eficiencia_General >= 96 AND kpi.Eficiencia_General < 98 THEN 1 ELSE 0 END) as Tecnicos_Bueno,
    SUM(CASE WHEN kpi.Eficiencia_General >= 94 AND kpi.Eficiencia_General < 96 THEN 1 ELSE 0 END) as Tecnicos_Aceptable,
    SUM(CASE WHEN kpi.Eficiencia_General >= 90 AND kpi.Eficiencia_General < 94 THEN 1 ELSE 0 END) as Tecnicos_Bajo,
    SUM(CASE WHEN kpi.Eficiencia_General < 90 THEN 1 ELSE 0 END) as Tecnicos_Critico
FROM vw_CalidadFlujo_kpi kpi
INNER JOIN tb_user_tqw u ON kpi.RUT_TECNICO = u.rut
WHERE kpi.MES_CONTABLE = '2025-05-01'
    AND kpi.RUT_TECNICO IN (
        '19777151-8', '19485174-K', '22064488-K', 
        '20189443-3', '20239971-1', '13091348-2'
    )
    AND u.vigente = 'Si'
```

---

## 10. Conclusiones Integradas: Productividad + Calidad

### 🎯 Hallazgos Clave del Análisis Completo:

#### **Técnicos con Desempeño Balanceado:**
1. **Jesús Lepe (22064488-K):** 
   - ✅ Calidad: 100% (Excelente)
   - ✅ Productividad: 32.86% completadas, 25% días improductivos
   - **Recomendación:** Modelo a replicar

#### **Técnicos con Alta Calidad, Baja Productividad:**
2. **Martín Aravena (20189443-3):**
   - ✅ Calidad: 100% (Excelente)
   - ❌ Productividad: 30.95% completadas, 47.1% días improductivos

3. **Carlos Correa (13091348-2):**
   - ✅ Calidad: 100% (Excelente)
   - ⚠️ Productividad: 33.33% completadas, 41.2% días improductivos

#### **Técnicos Críticos (Requieren Intervención):**
4. **Eduardo Espinoza (20239971-1):**
   - ❌ Calidad: 91.30% (Bajo meta)
   - ❌ Productividad: 21.57% completadas, 60% días improductivos
   - **Acción:** Plan de mejora integral urgente

5. **Luciano Aranda (19485174-K):**
   - ⚠️ Calidad: 93.94% (Límite)
   - ❌ Productividad: 28.57% completadas, 47.8% días improductivos
   - **Acción:** Monitoreo estrecho y capacitación

#### **Técnicos con Potencial:**
6. **Ruperto Rojas (19777151-8):**
   - ✅ Calidad: 97.96% (Bueno)
   - ⚠️ Productividad: 35.62% completadas, 47.4% días improductivos
   - **Oportunidad:** Mejorar consistencia operacional

---

### 📈 Recomendaciones Estratégicas:

#### **Corto Plazo (1-2 meses):**
1. **Plan de Mejora Eduardo Espinoza:** Capacitación técnica + acompañamiento diario
2. **Monitoreo Luciano Aranda:** Revisión semanal de procesos y calidad
3. **Optimización de Reagendamiento:** 67.47% de derivaciones requiere revisión

#### **Mediano Plazo (3-6 meses):**
1. **Programa de Mentoring:** Jesús Lepe como mentor de técnicos con bajo rendimiento
2. **Análisis de Procesos:** Identificar causas de días improductivos en técnicos con alta calidad
3. **Estandarización:** Documentar metodologías de técnicos con 100% calidad

#### **Largo Plazo (6+ meses):**
1. **Sistema de Incentivos:** Balancear productividad y calidad
2. **Capacitación Continua:** Programa de desarrollo técnico
3. **Mejora de Procesos:** Reducir motivos estructurales de no realización

---

### 📊 Indicadores de Seguimiento Propuestos:

| Indicador | Meta | Frecuencia | Responsable |
|-----------|------|------------|-------------|
| **Calidad Reactiva Consolidada** | ≥96% | Mensual | Supervisor + Técnico |
| **% Actividades Completadas** | ≥40% | Mensual | Supervisor |
| **% Días Improductivos** | ≤35% | Semanal | Supervisor |
| **Motivos P.Reagendamiento** | ≤60% | Quincenal | Operaciones |
| **"Sin moradores"** | ≤35% | Semanal | Gestión Territorio |

---

*Reporte completo actualizado con análisis de Calidad Reactiva Mayo 2025*  
*Metodología consolidada (no segmentada por tipo de red) aplicada según estándares de facturación*  
*Fecha: Julio 2025*